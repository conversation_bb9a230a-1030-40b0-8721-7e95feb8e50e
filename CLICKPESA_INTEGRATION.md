# ClickPesa Payment Integration Guide

This guide explains how to integrate ClickPesa payment gateway for Tanzania mobile money payments in your Flutter restaurant app.

## ✅ What's Already Done

### 1. Currency Localization
- ✅ Created `CurrencyFormatter` utility for Tanzanian Shillings (TZS)
- ✅ Updated all price displays throughout the app to use TZS formatting
- ✅ Consistent currency formatting in cart, checkout, and menu screens

### 2. ClickPesa Service Implementation
- ✅ Created `ClickPesaService` with full API integration
- ✅ Support for all major Tanzania mobile money providers:
  - M-Pesa
  - Tigo Pesa
  - Airtel Money
  - Halo Pesa
  - T-Pesa
- ✅ Phone number validation and formatting for Tanzania
- ✅ Payment status tracking and error handling

### 3. UI Components
- ✅ Created `ClickPesaPaymentWidget` for payment processing
- ✅ Integrated ClickPesa option in payment method screen
- ✅ Mobile-friendly payment interface with provider selection

### 4. Configuration
- ✅ Created `ClickPesaConfig` for easy credential management
- ✅ Environment-specific configuration (sandbox/production)
- ✅ Comprehensive error and success message handling

## 🔧 Setup Instructions

### Step 1: Get ClickPesa Credentials

1. **Sign up for ClickPesa**
   - Visit [https://clickpesa.com/](https://clickpesa.com/)
   - Create a merchant account
   - Complete the verification process

2. **Get API Credentials**
   - Log into your ClickPesa dashboard
   - Navigate to API settings
   - Copy your:
     - API Key
     - Secret Key
     - Merchant ID

### Step 2: Configure Credentials

Update `lib/config/clickpesa_config.dart`:

```dart
class ClickPesaConfig {
  // Replace with your actual credentials
  static const String apiKey = 'your_actual_api_key';
  static const String secretKey = 'your_actual_secret_key';
  static const String merchantId = 'your_actual_merchant_id';
  
  // Set to true for production
  static const bool isProduction = false; // Change to true for live payments
}
```

### Step 3: Test the Integration

1. **Run the app**
   ```bash
   flutter run
   ```

2. **Test payment flow**
   - Add items to cart
   - Go to checkout
   - Select "ClickPesa Mobile Money" as payment method
   - Choose a mobile money provider
   - Enter a test phone number: `+************`
   - Process payment

### Step 4: Production Deployment

1. **Update configuration**
   ```dart
   static const bool isProduction = true;
   ```

2. **Add real credentials**
   - Replace test credentials with production ones
   - Test with small amounts first

3. **Update callback URLs**
   ```dart
   static const String successCallbackUrl = 'https://yourapp.com/payment/success';
   static const String failureCallbackUrl = 'https://yourapp.com/payment/failure';
   ```

## 📱 Supported Mobile Money Providers

| Provider | Code | Display Name |
|----------|------|--------------|
| M-Pesa | `MPESA` | M-Pesa |
| Tigo Pesa | `TIGO_PESA` | Tigo Pesa |
| Airtel Money | `AIRTEL_MONEY` | Airtel Money |
| Halo Pesa | `HALO_PESA` | Halo Pesa |
| T-Pesa | `T_PESA` | T-Pesa |

## 💰 Currency Information

- **Currency**: Tanzanian Shilling (TZS)
- **Symbol**: TZS
- **Minimum Transaction**: TZS 100
- **Maximum Transaction**: TZS 10,000,000
- **Format**: TZS 1,234.56

## 🔒 Security Features

- ✅ API key authentication
- ✅ Request signing with secret key
- ✅ Phone number validation
- ✅ Amount validation
- ✅ Transaction timeout handling
- ✅ Error handling and logging

## 📞 Phone Number Formats

Supported formats for Tanzania:
- `+255712345678` (International format)
- `**********` (National format)
- `712345678` (Local format)

All formats are automatically converted to international format for API calls.

## 🧪 Testing

### Test Phone Numbers
Use these numbers for testing in sandbox mode:
- `+************` (Success)
- `+255987654321` (Insufficient funds)
- `+255555555555` (Timeout)

### Test Amounts
- `1000.00` - Success
- `500.00` - Insufficient funds
- `50.00` - Below minimum (will fail validation)

## 🚨 Troubleshooting

### Common Issues

1. **"Invalid credentials" error**
   - Check that you've updated the credentials in `ClickPesaConfig`
   - Verify credentials in your ClickPesa dashboard

2. **"Invalid phone number" error**
   - Ensure phone number is in Tanzania format
   - Check that number starts with +255, 0, or is 9 digits

3. **Payment timeout**
   - Check internet connection
   - Verify ClickPesa service status
   - Try with a different phone number

4. **"Service unavailable" error**
   - Check if you're using sandbox vs production URLs
   - Verify your ClickPesa account status

### Debug Mode

Enable debug logging by setting:
```dart
static const bool debugMode = true;
```

This will print detailed logs for troubleshooting.

## 📚 API Documentation

For detailed API documentation, visit:
- [ClickPesa API Docs](https://docs.clickpesa.com/)
- [Mobile Money Integration Guide](https://docs.clickpesa.com/mobile-money)

## 🎯 Next Steps

1. **Add payment logos**
   - Add mobile money provider logos to `assets/images/`
   - Update logo paths in `ClickPesaConfig`

2. **Implement webhooks**
   - Set up webhook endpoints for payment notifications
   - Handle payment status updates

3. **Add payment history**
   - Store payment records in your database
   - Implement payment history screen

4. **Enhanced error handling**
   - Add retry mechanisms
   - Implement offline payment queuing

## 💡 Tips for Success

1. **Start with sandbox testing**
2. **Test with small amounts first**
3. **Implement proper error handling**
4. **Monitor payment success rates**
5. **Keep credentials secure**
6. **Regular testing of payment flows**

## 📞 Support

For ClickPesa support:
- Email: <EMAIL>
- Phone: +255 XXX XXX XXX
- Documentation: https://docs.clickpesa.com/

---

**Your app now supports Tanzania mobile money payments through ClickPesa! 🎉**
