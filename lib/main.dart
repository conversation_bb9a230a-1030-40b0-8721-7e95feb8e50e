import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
// import 'package:flutter_stripe/flutter_stripe.dart';  // Temporarily disabled for POC
import 'package:smart_restaurant_menu/routes/routes.dart';
import 'package:smart_restaurant_menu/config/supabase_config.dart';
import 'package:smart_restaurant_menu/services/supabase_auth_bridge.dart';
import 'package:smart_restaurant_menu/services/supabase_storage_service.dart';

// import 'package:smart_restaurant_menu/services/payment_service.dart';  // Not needed in main
import 'package:smart_restaurant_menu/services/notification_service.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase first
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Initialize Supabase
  await Supabase.initialize(
    url: SupabaseConfig.supabaseUrl,
    anonKey: SupabaseConfig.supabaseAnonKey,
  );

  // Initialize Firebase-Supabase auth bridge
  await SupabaseAuthBridge.initialize();


  // Start app startup performance tracking after Firebase is initialized
  final appStartupTrace = FirebasePerformance.instance.newTrace('app_startup');
  await appStartupTrace.start();

  // Initialize Firebase Messaging background handler
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

  // Initialize Stripe (Mock implementation for POC)
  // Stripe.publishableKey = PaymentConfig.stripePublishableKey;
  // await Stripe.instance.applySettings();

  // Initialize Firebase Crashlytics
  FlutterError.onError = (errorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  };

  // Pass all uncaught asynchronous errors to Crashlytics
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };

  // Stop app startup trace after initialization
  await appStartupTrace.stop();

  runApp(ProviderScope(child: SmartRestaurantApp()));
}

class SmartRestaurantApp extends ConsumerWidget {
  const SmartRestaurantApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(goRouterProvider);
    return MaterialApp.router(
      routerConfig: router,
      debugShowCheckedModeBanner: false,
      title: 'Smart Restaurant Menu',
      theme: ThemeData(
        visualDensity: VisualDensity.adaptivePlatformDensity,
        useMaterial3: true,
        appBarTheme: AppBarTheme(
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        scaffoldBackgroundColor: Colors.white,
        cardColor: Colors.deepOrange[50],
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.deepOrange[700]!,
          brightness: Brightness.light,
        ),

        iconTheme: IconThemeData(color: Colors.deepOrange[700]),
        buttonTheme: ButtonThemeData(
          buttonColor: Colors.deepOrange[700],
          textTheme: ButtonTextTheme.primary,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.deepOrange[700],
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
    );
  }
}
