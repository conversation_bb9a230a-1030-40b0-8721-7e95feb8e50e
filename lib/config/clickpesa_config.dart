// File: lib/config/clickpesa_config.dart
/// Configuration for ClickPesa payment integration
///
/// To use ClickPesa in your app:
/// 1. Sign up at https://clickpesa.com/
/// 2. Get your API credentials from the dashboard
/// 3. Replace the placeholder values below with your actual credentials
/// 4. For production, consider using environment variables or secure storage
library;

class ClickPesaConfig {
  // TODO: Replace with your actual ClickPesa credentials
  // Get these from your ClickPesa dashboard
  static const String apiKey = 'SKFvPIrQT9dUeiTYAUbmmOl9B6hiHgE5SRYXoOS9Uf';
  static const String clientId = 'IDme5ATc4Z2PpipLTBO0KyK1spdDIlhd';
  static const String merchantId = 'your_merchant_id_here';
  static const String checksumKey = 'CHKrHaWbkvbcLnQGGTpbvJ2JGit0yT5rFwA';

  // API Configuration
  static const String baseUrl = 'https://api.clickpesa.com';
  static const String version = 'v1';

  // Supported payment methods in Tanzania
  static const List<String> supportedMobileMoney = [
    'MPESA',
    'TIGO_PESA',
    'AIRTEL_MONEY',
    'HALO_PESA',
    'T_PESA',
  ];

  // Currency configuration
  static const String currency = 'TZS';
  static const String currencySymbol = 'TZS';

  // Transaction limits (in TZS)
  static const double minTransactionAmount = 100.0;
  static const double maxTransactionAmount = 10000000.0; // 10 million TZS

  // Timeout configurations
  static const int apiTimeoutSeconds = 30;
  static const int paymentTimeoutSeconds = 300; // 5 minutes

  // Callback URLs (replace with your actual URLs)
  static const String successCallbackUrl =
      'https://yourapp.com/payment/success';
  static const String failureCallbackUrl =
      'https://yourapp.com/payment/failure';
  static const String cancelCallbackUrl = 'https://yourapp.com/payment/cancel';

  // Environment configuration
  static const bool isProduction = false; // Set to true for production

  // Get the appropriate base URL based on environment
  // Note: ClickPesa uses the same API URL for both testing and production
  // Testing is done with test credentials and account settings
  static String get apiBaseUrl {
    return 'https://api.clickpesa.com';
  }

  // Validation methods
  static bool get isConfigured {
    return apiKey != 'SKFvPIrQT9dUeiTYAUbmmOl9B6hiHgE5SRYXoOS9Uf' &&
        clientId != 'IDme5ATc4Z2PpipLTBO0KyK1spdDIlhd' &&
        checksumKey != 'CHKrHaWbkvbcLnQGGTpbvJ2JGit0yT5rFwA';
  }

  static bool isValidAmount(double amount) {
    return amount >= minTransactionAmount && amount <= maxTransactionAmount;
  }

  // Phone number validation for Tanzania
  static bool isValidTanzanianPhone(String phone) {
    // Remove spaces and special characters
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d+]'), '');

    // Tanzania mobile number patterns
    final patterns = [
      RegExp(r'^\+255[67]\d{8}$'), // +255 format
      RegExp(r'^255[67]\d{8}$'), // 255 format (ClickPesa preferred)
      RegExp(r'^0[67]\d{8}$'), // 0 format
      RegExp(r'^[67]\d{8}$'), // Without country code
    ];

    return patterns.any((pattern) => pattern.hasMatch(cleanPhone));
  }

  // Format phone number for ClickPesa API
  // ClickPesa requires: country code WITHOUT plus sign
  // Example: 255712345678 (not +255712345678)
  static String formatPhoneNumber(String phone) {
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d+]'), '');

    if (cleanPhone.startsWith('+255')) {
      return cleanPhone.substring(1); // Remove the + sign
    } else if (cleanPhone.startsWith('255')) {
      return cleanPhone; // Already in correct format
    } else if (cleanPhone.startsWith('0')) {
      return '255${cleanPhone.substring(1)}'; // Replace 0 with 255
    } else if (cleanPhone.length == 9) {
      return '255$cleanPhone'; // Add 255 prefix
    }

    return cleanPhone;
  }

  // Get mobile money provider display names
  static Map<String, String> get mobileMoneyProviders {
    return {
      'MPESA': 'M-Pesa',
      'TIGO_PESA': 'Tigo Pesa',
      'AIRTEL_MONEY': 'Airtel Money',
      'HALO_PESA': 'Halo Pesa',
      'T_PESA': 'T-Pesa',
    };
  }

  // Get mobile money provider logos (add these assets to your project)
  static Map<String, String> get mobileMoneyLogos {
    return {
      'MPESA': 'assets/images/mpesa_logo.png',
      'TIGO_PESA': 'assets/images/tigo_logo.png',
      'AIRTEL_MONEY': 'assets/images/airtel_logo.png',
      'HALO_PESA': 'assets/images/halo_logo.png',
      'T_PESA': 'assets/images/tpesa_logo.png',
    };
  }

  // Development/Testing configuration
  static const Map<String, String> testCredentials = {
    'test_phone': '+255123456789',
    'test_amount': '1000.00',
    'test_reference': 'TEST_REF_123',
  };

  // Error messages
  static const Map<String, String> errorMessages = {
    'invalid_credentials':
        'Invalid ClickPesa credentials. Please check your configuration.',
    'invalid_amount':
        'Amount must be between TZS $minTransactionAmount and TZS $maxTransactionAmount',
    'invalid_phone': 'Please enter a valid Tanzanian phone number',
    'network_error': 'Network error. Please check your internet connection.',
    'payment_timeout': 'Payment timed out. Please try again.',
    'payment_cancelled': 'Payment was cancelled by user.',
    'insufficient_funds': 'Insufficient funds in mobile money account.',
    'service_unavailable': 'Mobile money service is currently unavailable.',
  };

  // Success messages
  static const Map<String, String> successMessages = {
    'payment_initiated':
        'Payment request sent. Please check your phone for confirmation.',
    'payment_completed': 'Payment completed successfully!',
    'payment_pending':
        'Payment is being processed. You will receive a confirmation shortly.',
  };

  // Instructions for users
  static const Map<String, String> userInstructions = {
    'MPESA':
        'You will receive an M-Pesa prompt on your phone. Enter your M-Pesa PIN to complete the payment.',
    'TIGO_PESA':
        'You will receive a Tigo Pesa prompt on your phone. Enter your Tigo Pesa PIN to complete the payment.',
    'AIRTEL_MONEY':
        'You will receive an Airtel Money prompt on your phone. Enter your Airtel Money PIN to complete the payment.',
    'HALO_PESA':
        'You will receive a Halo Pesa prompt on your phone. Enter your Halo Pesa PIN to complete the payment.',
    'T_PESA':
        'You will receive a T-Pesa prompt on your phone. Enter your T-Pesa PIN to complete the payment.',
  };
}
