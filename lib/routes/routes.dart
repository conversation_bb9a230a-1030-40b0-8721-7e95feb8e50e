import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:smart_restaurant_menu/models/menu_item.dart';
import 'package:smart_restaurant_menu/routes/go_router_refresh_stream.dart';
import 'package:smart_restaurant_menu/screens/cart_screen.dart';
import 'package:smart_restaurant_menu/screens/login_screen.dart';
import 'package:smart_restaurant_menu/screens/menu_item_detail.dart';
import 'package:smart_restaurant_menu/screens/menu_screen.dart';
import 'package:smart_restaurant_menu/screens/order_screen.dart';
import 'package:smart_restaurant_menu/screens/profile_screen.dart';
import 'package:smart_restaurant_menu/screens/register.dart';
import 'package:smart_restaurant_menu/screens/scaffold_with_nav.dart';
import 'package:smart_restaurant_menu/screens/email_verification_screen.dart';
import 'package:smart_restaurant_menu/screens/password_reset_screen.dart';
import 'package:smart_restaurant_menu/screens/checkout_screen.dart';
import 'package:smart_restaurant_menu/screens/payment_success_screen.dart';
import 'package:smart_restaurant_menu/screens/splash_screen.dart';
import 'package:smart_restaurant_menu/screens/order_details_screen.dart';
import 'package:smart_restaurant_menu/screens/admin/admin_shell.dart';
import 'package:smart_restaurant_menu/screens/admin/admin_dashboard_screen.dart';
import 'package:smart_restaurant_menu/screens/admin/admin_orders_screen.dart';
import 'package:smart_restaurant_menu/screens/admin/admin_menu_screen.dart';
import 'package:smart_restaurant_menu/screens/admin/admin_analytics_screen.dart';
import 'package:smart_restaurant_menu/screens/admin/admin_settings_screen.dart';
import 'package:smart_restaurant_menu/services/auth_service.dart';
import 'package:smart_restaurant_menu/widgets/auth_wrapper.dart';

final _rootNavigatorKey = GlobalKey<NavigatorState>();

enum AppRoute {
  splash,
  login,
  register,
  emailVerification,
  passwordReset,
  menu,
  menuItem,
  orders,
  orderDetails,
  cart,
  profile,
  checkout,
  paymentSuccess,
  orderSuccess,
  // Admin routes
  adminDashboard,
  adminOrders,
  adminOrderDetails,
  adminMenu,
  adminAnalytics,
  adminSettings,
}

final goRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    refreshListenable: GoRouterRefreshStream(
      ref.watch(authServiceProvider.notifier).authStateChanges,
    ),
    initialLocation: '/splash',
    navigatorKey: _rootNavigatorKey,
    debugLogDiagnostics: true,
    // Minimal redirect - let AuthWrapper handle all authentication logic
    redirect: (context, state) {
      // No redirects - let AuthWrapper handle everything
      return null;
    },
    routes: [
      GoRoute(
        path: '/splash',
        name: AppRoute.splash.name,
        builder:
            (context, state) => AuthWrapper(
              currentRoute: state.uri.path,
              child: SplashScreen(),
            ),
      ),
      GoRoute(
        path: '/login',
        name: AppRoute.login.name,
        builder:
            (context, state) =>
                AuthWrapper(currentRoute: state.uri.path, child: LoginScreen()),
      ),
      GoRoute(
        path: '/register',
        name: AppRoute.register.name,
        builder: (context, state) => RegisterScreen(),
      ),
      GoRoute(
        path: '/email-verification',
        name: AppRoute.emailVerification.name,
        builder: (context, state) => EmailVerificationScreen(),
      ),
      GoRoute(
        path: '/password-reset',
        name: AppRoute.passwordReset.name,
        builder: (context, state) => PasswordResetScreen(),
      ),
      GoRoute(
        path: '/checkout',
        name: AppRoute.checkout.name,
        builder: (context, state) => CheckoutScreen(),
      ),
      GoRoute(
        path: '/payment-success',
        name: AppRoute.paymentSuccess.name,
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          return PaymentSuccessScreen(
            paymentIntentId: extra?['paymentIntentId'],
            amount: extra?['amount'] ?? 0.0,
          );
        },
      ),
      GoRoute(
        path: '/order-success',
        name: AppRoute.orderSuccess.name,
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          return PaymentSuccessScreen(
            paymentIntentId: extra?['transactionId'],
            amount: extra?['amount'] ?? 0.0,
            paymentMethod: extra?['paymentMethod'] ?? 'ClickPesa',
            orderReference: extra?['orderReference'],
            isClickPesaPayment: true,
          );
        },
      ),
      StatefulShellRoute.indexedStack(
        pageBuilder:
            (context, state, navigationShell) => NoTransitionPage(
              child: ScaffoldWithNestedNavigation(
                navigationShell: navigationShell,
              ),
            ),
        branches: [
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: "/",
                name: AppRoute.menu.name,
                builder:
                    (context, state) => AuthWrapper(
                      currentRoute: state.uri.path,
                      child: MenuScreen(),
                    ),
                routes: [
                  GoRoute(
                    path: "menu_item",
                    name: AppRoute.menuItem.name,
                    builder: (context, state) {
                      final item = state.extra as MenuItem;
                      return AuthWrapper(
                        currentRoute: state.uri.path,
                        child: ItemDetailScreen(item: item),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: "/orders",
                name: AppRoute.orders.name,
                builder: (context, state) => OrderScreen(),
                routes: [
                  GoRoute(
                    path: "/details/:orderId",
                    name: AppRoute.orderDetails.name,
                    builder: (context, state) {
                      final orderId = state.pathParameters['orderId']!;
                      return OrderDetailsScreen(orderId: orderId);
                    },
                  ),
                ],
              ),
            ],
          ),
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: "/cart",
                name: AppRoute.cart.name,
                builder: (context, state) => CartScreen(),
              ),
            ],
          ),
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: "/profile",
                name: AppRoute.profile.name,
                builder: (context, state) => ProfileScreen(),
              ),
            ],
          ),
        ],
      ),

      // Admin Routes
      StatefulShellRoute.indexedStack(
        pageBuilder:
            (context, state, navigationShell) => NoTransitionPage(
              child: AdminShell(navigationShell: navigationShell),
            ),
        branches: [
          // Admin Dashboard
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: "/admin/dashboard",
                name: AppRoute.adminDashboard.name,
                builder:
                    (context, state) => AuthWrapper(
                      currentRoute: state.uri.path,
                      child: const AdminDashboardScreen(),
                    ),
              ),
            ],
          ),

          // Admin Orders
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: "/admin/orders",
                name: AppRoute.adminOrders.name,
                builder:
                    (context, state) => AuthWrapper(
                      currentRoute: state.uri.path,
                      child: const AdminOrdersScreen(),
                    ),
                routes: [
                  GoRoute(
                    path: "/details/:orderId",
                    name: AppRoute.adminOrderDetails.name,
                    builder: (context, state) {
                      final orderId = state.pathParameters['orderId']!;
                      return AuthWrapper(
                        currentRoute: state.uri.path,
                        child: OrderDetailsScreen(orderId: orderId),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),

          // Admin Menu
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: "/admin/menu",
                name: AppRoute.adminMenu.name,
                builder: (context, state) => const AdminMenuScreen(),
              ),
            ],
          ),

          // Admin Analytics
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: "/admin/analytics",
                name: AppRoute.adminAnalytics.name,
                builder: (context, state) => const AdminAnalyticsScreen(),
              ),
            ],
          ),

          // Admin Settings
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: "/admin/settings",
                name: AppRoute.adminSettings.name,
                builder: (context, state) => const AdminSettingsScreen(),
              ),
            ],
          ),
        ],
      ),
    ],
  );
});
