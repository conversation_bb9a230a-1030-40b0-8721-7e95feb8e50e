import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/cart_provider.dart';
import '../models/menu_item.dart';
import '../utils/validators.dart';
import '../utils/currency_formatter.dart';
import 'payment_method_screen.dart';

class CheckoutScreen extends ConsumerStatefulWidget {
  const CheckoutScreen({super.key});

  @override
  ConsumerState<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends ConsumerState<CheckoutScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _notesController = TextEditingController();

  String _selectedDeliveryOption = 'delivery';
  bool _isProcessing = false;

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  double _calculateSubtotal(List<Map<String, dynamic>> cartItems) {
    return cartItems.fold(
      0.0,
      (sum, item) => sum + (item['totalPrice'] as double),
    );
  }

  double _calculateDeliveryFee() {
    return _selectedDeliveryOption == 'delivery' ? 2.99 : 0.0;
  }

  double _calculateTax(double subtotal) {
    return subtotal * 0.08; // 8% tax
  }

  double _calculateTotal(List<Map<String, dynamic>> cartItems) {
    final subtotal = _calculateSubtotal(cartItems);
    final deliveryFee = _calculateDeliveryFee();
    final tax = _calculateTax(subtotal);
    return subtotal + deliveryFee + tax;
  }

  Future<void> _proceedToPayment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final cartItems = ref.read(cartProvider);
    if (cartItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Your cart is empty'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      // Prepare order items for payment
      final orderItems =
          cartItems.map((item) {
            final menuItem = item['item'] as MenuItem;
            final addOns = item['addOns'] as List<Map<String, dynamic>>? ?? [];

            // Convert add-ons to serializable format
            final serializedAddOns =
                addOns.map((addOn) {
                  final addOnItem = addOn['item'] as MenuItem;
                  return {
                    'id': addOnItem.id,
                    'name': addOnItem.name,
                    'price': addOnItem.price,
                    'quantity': addOn['quantity'],
                    'totalPrice': addOnItem.price * addOn['quantity'],
                  };
                }).toList();

            return {
              'id': menuItem.id,
              'name': menuItem.name,
              'quantity': item['quantity'],
              'price': menuItem.price,
              'totalPrice': item['totalPrice'],
              'addOns': serializedAddOns,
            };
          }).toList();

      // Navigate to payment method screen
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) => PaymentMethodScreen(
                  totalAmount: _calculateTotal(cartItems),
                  orderItems: orderItems,
                ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cartItems = ref.watch(cartProvider);

    if (cartItems.isEmpty) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Checkout'),
          backgroundColor: theme.primaryColor,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.shopping_cart_outlined,
                size: 80,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'Your cart is empty',
                style: TextStyle(fontSize: 18, color: Colors.grey[600]),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => context.pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Continue Shopping'),
              ),
            ],
          ),
        ),
      );
    }

    final subtotal = _calculateSubtotal(cartItems);
    final deliveryFee = _calculateDeliveryFee();
    final tax = _calculateTax(subtotal);
    final total = _calculateTotal(cartItems);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Checkout'),
        backgroundColor: theme.primaryColor,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Order summary
                    _buildSectionTitle('Order Summary'),
                    _buildOrderSummary(cartItems),

                    const SizedBox(height: 24),

                    // Delivery options
                    _buildSectionTitle('Delivery Options'),
                    _buildDeliveryOptions(),

                    const SizedBox(height: 24),

                    // Customer information
                    _buildSectionTitle('Customer Information'),
                    _buildCustomerForm(),

                    const SizedBox(height: 24),

                    // Order total
                    _buildSectionTitle('Order Total'),
                    _buildOrderTotal(subtotal, deliveryFee, tax, total),
                  ],
                ),
              ),
            ),
          ),

          // Proceed to payment button
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 5,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isProcessing ? null : _proceedToPayment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child:
                    _isProcessing
                        ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                        : Text(
                          'Proceed to Payment - TZS ${total.toStringAsFixed(2)}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  Widget _buildOrderSummary(List<Map<String, dynamic>> cartItems) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children:
              cartItems.map((item) {
                final menuItem = item['item'] as MenuItem;
                final quantity = item['quantity'] as int;
                final totalPrice = item['totalPrice'] as double;

                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              menuItem.name,
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              'Qty: $quantity',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Text(
                        CurrencyFormatter.format(totalPrice),
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                    ],
                  ),
                );
              }).toList(),
        ),
      ),
    );
  }

  Widget _buildDeliveryOptions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            RadioListTile<String>(
              title: const Text('Delivery'),
              subtitle: const Text('Delivered to your address'),
              value: 'delivery',
              groupValue: _selectedDeliveryOption,
              onChanged: (value) {
                setState(() {
                  _selectedDeliveryOption = value!;
                });
              },
              activeColor: Theme.of(context).primaryColor,
            ),
            RadioListTile<String>(
              title: const Text('Pickup'),
              subtitle: const Text('Pick up from restaurant'),
              value: 'pickup',
              groupValue: _selectedDeliveryOption,
              onChanged: (value) {
                setState(() {
                  _selectedDeliveryOption = value!;
                });
              },
              activeColor: Theme.of(context).primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerForm() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            TextFormField(
              controller: _nameController,
              validator: Validators.validateName,
              decoration: const InputDecoration(
                labelText: 'Full Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _phoneController,
              validator: Validators.validatePhoneNumber,
              keyboardType: TextInputType.phone,
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                border: OutlineInputBorder(),
              ),
            ),
            if (_selectedDeliveryOption == 'delivery') ...[
              const SizedBox(height: 16),
              TextFormField(
                controller: _addressController,
                validator: Validators.validateAddress,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'Delivery Address',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              maxLines: 2,
              decoration: const InputDecoration(
                labelText: 'Special Instructions (Optional)',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderTotal(
    double subtotal,
    double deliveryFee,
    double tax,
    double total,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildTotalRow('Subtotal', subtotal),
            if (deliveryFee > 0) _buildTotalRow('Delivery Fee', deliveryFee),
            _buildTotalRow('Tax', tax),
            const Divider(),
            _buildTotalRow('Total', total, isTotal: true),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? Theme.of(context).primaryColor : null,
            ),
          ),
          Text(
            'TZS ${amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? Theme.of(context).primaryColor : null,
            ),
          ),
        ],
      ),
    );
  }
}
