import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:smart_restaurant_menu/models/user.dart';
import '../services/auth_service.dart';
import '../services/database_service.dart';
import '../services/storage_service.dart';
import '../models/order.dart';
import '../routes/routes.dart';

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  final _nameController = TextEditingController();
  final _preferencesController = TextEditingController();
  bool _isEditing = false;
  bool _isUploading = false;
  List<OrderModel> _recentOrders = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeControllers();
      _loadRecentOrders();
    });
  }

  void _initializeControllers() {
    final user = ref.read(authServiceProvider).user;
    if (user != null) {
      _nameController.text = user.name;
      _preferencesController.text = user.preferences.join(', ');
    }
  }

  Future<void> _loadRecentOrders() async {
    final user = ref.read(authServiceProvider).user;
    if (user != null) {
      try {
        final orders = await ref
            .read(databaseServiceProvider)
            .getUserOrders(user.uid);
        if (mounted) {
          setState(() {
            _recentOrders = orders.take(3).toList(); // Show only 3 most recent
          });
        }
      } catch (e) {
        // Handle error silently for now
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _preferencesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authServiceProvider);
    final user = authState.user;
    final theme = Theme.of(context);

    if (user == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Profile')),
        body: const Center(child: Text('Please log in to view your profile')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        backgroundColor: theme.primaryColor,
        actions: [
          IconButton(
            icon: Icon(_isEditing ? Icons.check : Icons.edit),
            onPressed: () {
              if (_isEditing) {
                _saveProfile(user);
              }
              setState(() {
                _isEditing = !_isEditing;
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => _showLogoutDialog(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 20),
              // Profile avatar with tap to change
              GestureDetector(
                onTap: _changeProfilePicture,
                child: Stack(
                  children: [
                    CircleAvatar(
                      radius: 60,
                      backgroundColor: theme.primaryColor.withOpacity(0.2),
                      backgroundImage:
                          user.profileImageUrl != null &&
                                  user.profileImageUrl!.isNotEmpty
                              ? NetworkImage(user.profileImageUrl!)
                              : null,
                      child:
                          user.profileImageUrl == null ||
                                  user.profileImageUrl!.isEmpty
                              ? Text(
                                user.name.isNotEmpty
                                    ? user.name[0].toUpperCase()
                                    : '?',
                                style: TextStyle(
                                  fontSize: 48,
                                  fontWeight: FontWeight.bold,
                                  color: theme.primaryColor,
                                ),
                              )
                              : null,
                    ),
                    if (_isUploading)
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black45,
                            shape: BoxShape.circle,
                          ),
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        ),
                      ),
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: theme.primaryColor,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.camera_alt,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Profile info card
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Personal Information',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Divider(),
                      const SizedBox(height: 8),

                      // Name field
                      _buildInfoRow(
                        'Name',
                        _isEditing
                            ? TextField(
                              controller: _nameController,
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                              ),
                            )
                            : Text(
                              user.name,
                              style: const TextStyle(fontSize: 16),
                            ),
                      ),

                      // Email field (non-editable)
                      _buildInfoRow(
                        'Email',
                        Text(user.email, style: const TextStyle(fontSize: 16)),
                      ),

                      // Preferences field
                      _buildInfoRow(
                        'Dietary Preferences',
                        _isEditing
                            ? TextField(
                              controller: _preferencesController,
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                hintText:
                                    'e.g., vegetarian, spicy, gluten-free',
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                              ),
                              maxLines: 2,
                            )
                            : user.preferences.isEmpty
                            ? const Text(
                              'None specified',
                              style: TextStyle(
                                fontSize: 16,
                                fontStyle: FontStyle.italic,
                                color: Colors.grey,
                              ),
                            )
                            : Wrap(
                              spacing: 8,
                              runSpacing: 4,
                              children:
                                  user.preferences.map((preference) {
                                    return Chip(
                                      label: Text(
                                        preference,
                                        style: const TextStyle(fontSize: 12),
                                      ),
                                      backgroundColor: theme.primaryColor
                                          .withOpacity(0.1),
                                      labelStyle: TextStyle(
                                        color: theme.primaryColor,
                                      ),
                                    );
                                  }).toList(),
                            ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Order history card
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Order History',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          TextButton(
                            onPressed: () {
                              // Navigate to detailed order history
                              Navigator.pushNamed(context, '/orders');
                            },
                            child: const Text('View All'),
                          ),
                        ],
                      ),
                      const Divider(),

                      // Order history preview
                      _recentOrders.isEmpty
                          ? const Padding(
                            padding: EdgeInsets.symmetric(vertical: 16.0),
                            child: Center(child: Text('No orders yet')),
                          )
                          : ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _recentOrders.length,
                            itemBuilder: (context, index) {
                              final OrderModel order = _recentOrders[index];
                              return ListTile(
                                contentPadding: EdgeInsets.zero,
                                title: Text('Order #${order.id}'),
                                subtitle: Text(
                                  'Status: ${order.status.name} | Total: TZS ${order.total.toStringAsFixed(2)}',
                                ),
                                trailing: const Icon(Icons.chevron_right),
                                onTap: () {
                                  context.goNamed(
                                    AppRoute.orderDetails.name,
                                    pathParameters: {'orderId': order.id},
                                  );
                                },
                              );
                            },
                          ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Favorite items card
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Favorite Items',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          TextButton(
                            onPressed: () {
                              // Navigate to favorites
                            },
                            child: const Text('View All'),
                          ),
                        ],
                      ),
                      const Divider(),

                      // Favorites preview
                      user.favorites.isEmpty ?? true
                          ? const Padding(
                            padding: EdgeInsets.symmetric(vertical: 16.0),
                            child: Center(child: Text('No favorite items yet')),
                          )
                          : const Text('Your favorite items will appear here'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, Widget content) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          content,
        ],
      ),
    );
  }

  void _saveProfile(AppUser user) async {
    final updatedUser = user.copyWith(
      name: _nameController.text,
      preferences:
          _preferencesController.text
              .split(',')
              .map((e) => e.trim())
              .where((e) => e.isNotEmpty)
              .toList(),
      updatedAt: DateTime.now(),
    );

    await ref.read(databaseServiceProvider).saveUser(updatedUser);
    // Use the new refreshUser method to update the state
    await ref.read(authServiceProvider.notifier).refreshUser(user.uid);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Profile updated successfully')),
      );
    }
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Logout'),
            content: const Text('Are you sure you want to logout?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  ref.read(authServiceProvider.notifier).signOut();
                  Navigator.pushReplacementNamed(context, '/login');
                },
                child: const Text('Logout'),
              ),
            ],
          ),
    );
  }

  Future<void> _changeProfilePicture() async {
    try {
      final ImagePicker picker = ImagePicker();

      // Show options dialog
      showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return SafeArea(
            child: Wrap(
              children: <Widget>[
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('Choose from Gallery'),
                  onTap: () async {
                    Navigator.pop(context);
                    try {
                      final XFile? image = await picker.pickImage(
                        source: ImageSource.gallery,
                        maxWidth: 1000,
                        maxHeight: 1000,
                        imageQuality: 85,
                      );
                      if (image != null && mounted) {
                        _uploadProfilePicture(File(image.path));
                      }
                    } catch (e) {
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Error selecting image: $e')),
                        );
                      }
                    }
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.photo_camera),
                  title: const Text('Take a Photo'),
                  onTap: () async {
                    Navigator.pop(context);
                    try {
                      final XFile? photo = await picker.pickImage(
                        source: ImageSource.camera,
                        maxWidth: 1000,
                        maxHeight: 1000,
                        imageQuality: 85,
                      );
                      if (photo != null && mounted) {
                        _uploadProfilePicture(File(photo.path));
                      }
                    } catch (e) {
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Error taking photo: $e')),
                        );
                      }
                    }
                  },
                ),
              ],
            ),
          );
        },
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error initializing image picker: $e')),
        );
      }
    }
  }

  Future<void> _uploadProfilePicture(File imageFile) async {
    final user = ref.read(authServiceProvider).user;
    if (user == null) return;

    // Show loading indicator
    setState(() {
      _isUploading = true;
    });

    try {
      // Upload image to storage
      final imageUrl = await ref
          .read(storageServiceProvider)
          .uploadProfileImage(user.uid, imageFile);

      // Update user profile with new image URL
      final updatedUser = user.copyWith(profileImageUrl: imageUrl);
      await ref.read(databaseServiceProvider).saveUser(updatedUser);
      await ref.read(authServiceProvider.notifier).refreshUser(user.uid);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Profile picture updated successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to update profile picture: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }
}
