import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/auth_service.dart';
import '../../services/settings_service.dart';
import '../../widgets/admin/settings_forms.dart';
import '../../widgets/admin/staff_management.dart';

class AdminSettingsScreen extends ConsumerStatefulWidget {
  const AdminSettingsScreen({super.key});

  @override
  ConsumerState<AdminSettingsScreen> createState() =>
      _AdminSettingsScreenState();
}

class _AdminSettingsScreenState extends ConsumerState<AdminSettingsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authServiceProvider);
    final user = authState.user;
    final theme = Theme.of(context);

    if (user == null || !user.isAdminUser) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Access Denied'),
          backgroundColor: theme.primaryColor,
        ),
        body: const Center(
          child: Text('You do not have permission to access this area.'),
        ),
      );
    }

    final settingsAsync = ref.watch(restaurantSettingsStreamProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Restaurant Settings'),
        backgroundColor: theme.primaryColor,
        actions: [
          IconButton(
            icon: const Icon(Icons.backup),
            onPressed: _backupData,
            tooltip: 'Backup Data',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: _handleMenuAction,
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'export_csv',
                    child: Row(
                      children: [
                        Icon(Icons.table_chart),
                        SizedBox(width: 8),
                        Text('Export CSV'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'export_json',
                    child: Row(
                      children: [
                        Icon(Icons.code),
                        SizedBox(width: 8),
                        Text('Export JSON'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'system_info',
                    child: Row(
                      children: [
                        Icon(Icons.info),
                        SizedBox(width: 8),
                        Text('System Info'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          tabs: const [
            Tab(text: 'Basic Info'),
            Tab(text: 'Hours'),
            Tab(text: 'Payment'),
            Tab(text: 'Notifications'),
            Tab(text: 'Staff'),
            Tab(text: 'System'),
          ],
        ),
      ),
      body: settingsAsync.when(
        data:
            (settings) => TabBarView(
              controller: _tabController,
              children: [
                _buildBasicInfoTab(settings),
                _buildOperatingHoursTab(settings),
                _buildPaymentTab(settings),
                _buildNotificationsTab(settings),
                _buildStaffTab(),
                _buildSystemTab(),
              ],
            ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error:
            (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('Error loading settings: $error'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed:
                        () => ref.refresh(restaurantSettingsStreamProvider),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
      ),
    );
  }

  Widget _buildBasicInfoTab(settings) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: BasicInfoForm(settings: settings),
    );
  }

  Widget _buildOperatingHoursTab(settings) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: OperatingHoursForm(settings: settings),
    );
  }

  Widget _buildPaymentTab(settings) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: PaymentSettingsForm(settings: settings),
    );
  }

  Widget _buildNotificationsTab(settings) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: NotificationSettingsForm(settings: settings),
    );
  }

  Widget _buildStaffTab() {
    return const SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: StaffManagementWidget(),
    );
  }

  Widget _buildSystemTab() {
    final systemInfoAsync = ref.watch(systemInfoProvider);
    final activitiesAsync = ref.watch(settingsActivitiesStreamProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // System Information
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'System Information',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),

                  systemInfoAsync.when(
                    data:
                        (info) => Column(
                          children: [
                            _buildInfoRow(
                              'Total Orders',
                              '${info['totalOrders']}',
                            ),
                            _buildInfoRow(
                              'Total Menu Items',
                              '${info['totalMenuItems']}',
                            ),
                            _buildInfoRow(
                              'Total Users',
                              '${info['totalUsers']}',
                            ),
                            _buildInfoRow(
                              'Database Size',
                              '${info['databaseSize'].toStringAsFixed(2)} MB',
                            ),
                            _buildInfoRow('App Version', info['appVersion']),
                            _buildInfoRow(
                              'Last Updated',
                              info['lastUpdated'].toString().split('T')[0],
                            ),
                          ],
                        ),
                    loading: () => const CircularProgressIndicator(),
                    error: (error, stack) => Text('Error: $error'),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Recent Activities
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Recent Settings Activities',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),

                  activitiesAsync.when(
                    data:
                        (activities) =>
                            activities.isEmpty
                                ? const Text('No recent activities')
                                : ListView.builder(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: activities.take(10).length,
                                  itemBuilder: (context, index) {
                                    final activity = activities[index];
                                    return ListTile(
                                      leading: const Icon(Icons.history),
                                      title: Text(
                                        _formatActivityAction(
                                          activity['action'],
                                        ),
                                      ),
                                      subtitle: Text(
                                        DateTime.parse(
                                          activity['timestamp'],
                                        ).toString().split('.')[0],
                                      ),
                                      dense: true,
                                    );
                                  },
                                ),
                    loading: () => const CircularProgressIndicator(),
                    error: (error, stack) => Text('Error: $error'),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // System Actions
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'System Actions',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),

                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _backupData,
                          icon: const Icon(Icons.backup),
                          label: const Text('Backup Data'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _exportData('csv'),
                          icon: const Icon(Icons.download),
                          label: const Text('Export Data'),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _clearCache,
                      icon: const Icon(Icons.clear_all),
                      label: const Text('Clear Cache'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w600)),
          Text(value),
        ],
      ),
    );
  }

  String _formatActivityAction(String action) {
    switch (action) {
      case 'update_restaurant_settings':
        return 'Updated restaurant settings';
      case 'update_staff_role':
        return 'Updated staff role';
      case 'deactivate_staff':
        return 'Deactivated staff member';
      case 'export_data':
        return 'Exported data';
      case 'backup_data':
        return 'Backed up data';
      default:
        return action.replaceAll('_', ' ').toUpperCase();
    }
  }

  // Action handlers
  void _backupData() async {
    try {
      await ref.read(settingsServiceProvider).backupData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Data backup initiated'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Backup failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _exportData(String format) async {
    try {
      await ref.read(settingsServiceProvider).exportData(format);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Data export as ${format.toUpperCase()} initiated'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _clearCache() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Clear Cache'),
            content: const Text(
              'This will clear all cached data and refresh the app. '
              'Are you sure you want to continue?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // Clear all providers
                  ref.invalidate(restaurantSettingsStreamProvider);
                  ref.invalidate(staffListStreamProvider);
                  ref.invalidate(systemInfoProvider);
                  ref.invalidate(settingsActivitiesStreamProvider);

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Cache cleared successfully'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                child: const Text('Clear'),
              ),
            ],
          ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export_csv':
        _exportData('csv');
        break;
      case 'export_json':
        _exportData('json');
        break;
      case 'system_info':
        _showSystemInfoDialog();
        break;
    }
  }

  void _showSystemInfoDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('System Information'),
            content: Consumer(
              builder: (context, ref, child) {
                final systemInfoAsync = ref.watch(systemInfoProvider);

                return systemInfoAsync.when(
                  data:
                      (info) => Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildInfoRow(
                            'Total Orders',
                            '${info['totalOrders']}',
                          ),
                          _buildInfoRow(
                            'Total Menu Items',
                            '${info['totalMenuItems']}',
                          ),
                          _buildInfoRow('Total Users', '${info['totalUsers']}'),
                          _buildInfoRow(
                            'Database Size',
                            '${info['databaseSize'].toStringAsFixed(2)} MB',
                          ),
                          _buildInfoRow('App Version', info['appVersion']),
                        ],
                      ),
                  loading: () => const CircularProgressIndicator(),
                  error: (error, stack) => Text('Error: $error'),
                );
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }
}
