import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/order.dart';
import '../../services/admin_order_service.dart';
import '../../services/auth_service.dart';
import '../../widgets/admin/admin_app_bar.dart';
import '../../widgets/admin/admin_order_card.dart';
import '../../widgets/admin/order_status_chip.dart';
import '../../widgets/admin/staff_assignment_dialog.dart';

class AdminOrdersScreen extends ConsumerStatefulWidget {
  const AdminOrdersScreen({super.key});

  @override
  ConsumerState<AdminOrdersScreen> createState() => _AdminOrdersScreenState();
}

class _AdminOrdersScreenState extends ConsumerState<AdminOrdersScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  OrderStatus? _selectedStatusFilter;
  String _searchQuery = '';
  final _searchController = TextEditingController();
  List<String> _selectedOrderIds = [];
  bool _isSelectionMode = false;
  final List<OrderModel> _currentFilteredOrders = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authServiceProvider);
    final user = authState.user;
    final theme = Theme.of(context);

    if (user == null || !user.isAdminUser) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Access Denied'),
          backgroundColor: theme.primaryColor,
        ),
        body: const Center(
          child: Text('You do not have permission to access this area.'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Order Management'),
        backgroundColor: theme.primaryColor,
        actions: [
          if (_isSelectionMode) ...[
            IconButton(
              icon: const Icon(Icons.select_all),
              onPressed: _selectAll,
              tooltip: 'Select All',
            ),
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: _clearSelection,
              tooltip: 'Clear Selection',
            ),
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert),
              tooltip: 'Bulk Actions',
              onSelected: _handleBulkAction,
              itemBuilder:
                  (context) => [
                    const PopupMenuItem(
                      value: 'update_status',
                      child: Row(
                        children: [
                          Icon(Icons.update),
                          SizedBox(width: 8),
                          Text('Update Status'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'assign_staff',
                      child: Row(
                        children: [
                          Icon(Icons.people),
                          SizedBox(width: 8),
                          Text('Assign to Staff'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'unassign_staff',
                      child: Row(
                        children: [
                          Icon(Icons.person_remove, color: Colors.red),
                          SizedBox(width: 8),
                          Text(
                            'Unassign Staff',
                            style: TextStyle(color: Colors.red),
                          ),
                        ],
                      ),
                    ),
                  ],
            ),
          ] else ...[
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: _showSearchDialog,
              tooltip: 'Search Orders',
            ),
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: _showFilterDialog,
              tooltip: 'Filter Orders',
            ),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                // Refresh is automatic with streams, but we can show a snackbar
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Orders refreshed'),
                    duration: Duration(seconds: 1),
                  ),
                );
              },
              tooltip: 'Refresh',
            ),
          ],
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          tabs: const [
            Tab(text: 'All Orders'),
            Tab(text: 'Pending'),
            Tab(text: 'Today'),
            Tab(text: 'Statistics'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllOrdersTab(),
          _buildPendingOrdersTab(),
          _buildTodayOrdersTab(),
          _buildStatisticsTab(),
        ],
      ),
    );
  }

  Widget _buildAllOrdersTab() {
    final ordersAsync = ref.watch(allOrdersStreamProvider);

    return ordersAsync.when(
      data: (orders) => _buildOrdersList(orders),
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text('Error loading orders: $error'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => ref.refresh(allOrdersStreamProvider),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildPendingOrdersTab() {
    final ordersAsync = ref.watch(pendingOrdersStreamProvider);

    return ordersAsync.when(
      data: (orders) => _buildOrdersList(orders),
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stack) =>
              Center(child: Text('Error loading pending orders: $error')),
    );
  }

  Widget _buildTodayOrdersTab() {
    final ordersAsync = ref.watch(todayOrdersStreamProvider);

    return ordersAsync.when(
      data: (orders) => _buildOrdersList(orders),
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stack) =>
              Center(child: Text('Error loading today\'s orders: $error')),
    );
  }

  Widget _buildStatisticsTab() {
    final statsAsync = ref.watch(orderStatisticsProvider);

    return statsAsync.when(
      data: (stats) => _buildStatisticsView(stats),
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stack) =>
              Center(child: Text('Error loading statistics: $error')),
    );
  }

  Widget _buildOrdersList(List<OrderModel> allOrders) {
    // Apply filters
    List<OrderModel> filteredOrders = allOrders;

    if (_selectedStatusFilter != null) {
      filteredOrders =
          filteredOrders
              .where((order) => order.status == _selectedStatusFilter)
              .toList();
    }

    if (_searchQuery.isNotEmpty) {
      filteredOrders =
          filteredOrders.where((order) {
            return order.id.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                (order.customerInfo?['name']?.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ) ??
                    false);
          }).toList();
    }

    // Update current filtered orders for select all functionality
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _currentFilteredOrders.clear();
          _currentFilteredOrders.addAll(filteredOrders);
        });
      }
    });

    if (filteredOrders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty || _selectedStatusFilter != null
                  ? 'No orders match your filters'
                  : 'No orders found',
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
            ),
            if (_searchQuery.isNotEmpty || _selectedStatusFilter != null) ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _clearFilters,
                child: const Text('Clear Filters'),
              ),
            ],
          ],
        ),
      );
    }

    return Column(
      children: [
        // Filter chips
        if (_selectedStatusFilter != null || _searchQuery.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            child: Wrap(
              spacing: 8,
              children: [
                if (_selectedStatusFilter != null)
                  Chip(
                    label: Text(
                      'Status: ${_selectedStatusFilter!.displayName}',
                    ),
                    onDeleted: () {
                      setState(() {
                        _selectedStatusFilter = null;
                      });
                    },
                  ),
                if (_searchQuery.isNotEmpty)
                  Chip(
                    label: Text('Search: $_searchQuery'),
                    onDeleted: () {
                      setState(() {
                        _searchQuery = '';
                        _searchController.clear();
                      });
                    },
                  ),
              ],
            ),
          ),

        // Orders list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredOrders.length,
            itemBuilder: (context, index) {
              final order = filteredOrders[index];
              final isSelected = _selectedOrderIds.contains(order.id);

              return AdminOrderCard(
                order: order,
                isSelected: isSelected,
                onTap:
                    _isSelectionMode
                        ? () => _toggleOrderSelection(order.id)
                        : null,
                onLongPress: () => _toggleSelectionMode(order.id),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStatisticsView(Map<String, dynamic> stats) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Overview Cards
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Orders',
                  '${stats['totalOrders']}',
                  Icons.shopping_cart,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Total Revenue',
                  'TZS ${(stats['totalRevenue'] as double).toStringAsFixed(0)}',
                  Icons.attach_money,
                  Colors.green,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Today\'s Orders',
                  '${stats['todayOrders']}',
                  Icons.today,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Pending Orders',
                  '${stats['pendingOrders']}',
                  Icons.pending_actions,
                  Colors.red,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Status Breakdown
          const Text(
            'Order Status Breakdown',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children:
                    OrderStatus.values.map((status) {
                      final count =
                          (stats['statusCounts'] as Map<String, int>)[status
                              .name] ??
                          0;
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                OrderStatusChip(status: status),
                                const SizedBox(width: 12),
                                Text(status.displayName),
                              ],
                            ),
                            Text(
                              '$count',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(icon, color: color, size: 24),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  // Selection and filter methods
  void _toggleSelectionMode(String orderId) {
    setState(() {
      if (_isSelectionMode) {
        _toggleOrderSelection(orderId);
      } else {
        _isSelectionMode = true;
        _selectedOrderIds = [orderId];
      }
    });
  }

  void _toggleOrderSelection(String orderId) {
    setState(() {
      if (_selectedOrderIds.contains(orderId)) {
        _selectedOrderIds.remove(orderId);
        if (_selectedOrderIds.isEmpty) {
          _isSelectionMode = false;
        }
      } else {
        _selectedOrderIds.add(orderId);
      }
    });
  }

  void _selectAll() {
    setState(() {
      if (_currentFilteredOrders.isEmpty) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('No orders to select')));
        return;
      }

      // Check if all visible orders are already selected
      final visibleOrderIds =
          _currentFilteredOrders.map((order) => order.id).toSet();
      final selectedVisibleIds =
          _selectedOrderIds.where((id) => visibleOrderIds.contains(id)).toSet();

      if (selectedVisibleIds.length == visibleOrderIds.length) {
        // All visible orders are selected, so deselect them
        _selectedOrderIds.removeWhere((id) => visibleOrderIds.contains(id));
        if (_selectedOrderIds.isEmpty) {
          _isSelectionMode = false;
        }
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Deselected ${visibleOrderIds.length} orders'),
          ),
        );
      } else {
        // Select all visible orders
        _isSelectionMode = true;
        for (final order in _currentFilteredOrders) {
          if (!_selectedOrderIds.contains(order.id)) {
            _selectedOrderIds.add(order.id);
          }
        }
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Selected ${visibleOrderIds.length} orders')),
        );
      }
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedOrderIds.clear();
      _isSelectionMode = false;
    });
  }

  void _clearFilters() {
    setState(() {
      _selectedStatusFilter = null;
      _searchQuery = '';
      _searchController.clear();
    });
  }

  void _handleBulkAction(String action) {
    switch (action) {
      case 'update_status':
        _showBulkStatusUpdateDialog();
        break;
      case 'assign_staff':
        _showBulkStaffAssignmentDialog();
        break;
      case 'unassign_staff':
        _bulkUnassignStaff();
        break;
    }
  }

  void _showBulkStatusUpdateDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Update ${_selectedOrderIds.length} Orders'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Select new status for all selected orders:'),
                const SizedBox(height: 16),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children:
                      OrderStatus.values.map((status) {
                        return ActionChip(
                          label: Text(status.displayName),
                          avatar: OrderStatusChip(status: status),
                          onPressed: () {
                            Navigator.pop(context);
                            _bulkUpdateStatus(status);
                          },
                        );
                      }).toList(),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
            ],
          ),
    );
  }

  void _showBulkStaffAssignmentDialog() {
    showDialog(
      context: context,
      builder:
          (context) => StaffAssignmentDialog(
            orderIds: _selectedOrderIds,
            onAssign: _bulkAssignToStaff,
          ),
    );
  }

  Future<void> _bulkAssignToStaff(String staffId, String? note) async {
    if (_selectedOrderIds.isEmpty) return;

    try {
      await ref
          .read(adminOrderServiceProvider)
          .bulkAssignOrdersToStaff(
            orderIds: _selectedOrderIds,
            staffId: staffId,
            note: note,
          );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Assigned ${_selectedOrderIds.length} orders to staff',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }

      _clearSelection();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to assign orders: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _bulkUnassignStaff() async {
    if (_selectedOrderIds.isEmpty) return;

    try {
      for (final orderId in _selectedOrderIds) {
        await ref
            .read(adminOrderServiceProvider)
            .unassignOrderFromStaff(
              orderId: orderId,
              note: 'Bulk unassigned from admin panel',
            );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Unassigned ${_selectedOrderIds.length} orders from staff',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }

      _clearSelection();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to unassign orders: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _bulkUpdateStatus(OrderStatus newStatus) async {
    if (_selectedOrderIds.isEmpty) return;

    final user = ref.read(authServiceProvider).user;
    if (user == null) return;

    try {
      await ref
          .read(adminOrderServiceProvider)
          .bulkUpdateOrderStatus(
            orderIds: _selectedOrderIds,
            newStatus: newStatus,
            staffId: user.uid,
            note: 'Bulk status update from admin panel',
          );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Updated ${_selectedOrderIds.length} orders to ${newStatus.displayName}',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }

      _clearSelection();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update orders: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Search Orders'),
            content: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Enter order ID or customer name...',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _searchQuery = _searchController.text;
                  });
                  Navigator.pop(context);
                },
                child: const Text('Search'),
              ),
            ],
          ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Advanced Filters'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status Filter
                  const Text(
                    'Filter by Status:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    children: [
                      FilterChip(
                        label: const Text('All'),
                        selected: _selectedStatusFilter == null,
                        onSelected: (selected) {
                          setState(() {
                            _selectedStatusFilter = null;
                          });
                        },
                      ),
                      ...OrderStatus.values.map((status) {
                        return FilterChip(
                          label: Text(status.displayName),
                          selected: _selectedStatusFilter == status,
                          onSelected: (selected) {
                            setState(() {
                              _selectedStatusFilter = selected ? status : null;
                            });
                          },
                        );
                      }),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Quick Filter Presets
                  const Text(
                    'Quick Filters:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    children: [
                      ActionChip(
                        label: const Text('Urgent Orders'),
                        avatar: const Icon(
                          Icons.warning,
                          size: 16,
                          color: Colors.red,
                        ),
                        onPressed: () {
                          setState(() {
                            _selectedStatusFilter = OrderStatus.pending;
                          });
                          Navigator.pop(context);
                        },
                      ),
                      ActionChip(
                        label: const Text('Ready for Delivery'),
                        avatar: const Icon(Icons.delivery_dining, size: 16),
                        onPressed: () {
                          setState(() {
                            _selectedStatusFilter = OrderStatus.ready;
                          });
                          Navigator.pop(context);
                        },
                      ),
                      ActionChip(
                        label: const Text('In Progress'),
                        avatar: const Icon(Icons.restaurant, size: 16),
                        onPressed: () {
                          setState(() {
                            _selectedStatusFilter = OrderStatus.preparing;
                          });
                          Navigator.pop(context);
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedStatusFilter = null;
                    _searchQuery = '';
                    _searchController.clear();
                  });
                  Navigator.pop(context);
                },
                child: const Text('Clear All'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }
}
