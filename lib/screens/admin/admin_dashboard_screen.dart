import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../services/auth_service.dart';
import '../../services/admin_order_service.dart';
import '../../services/analytics_service.dart';
import '../../models/user.dart';
import '../../models/order.dart';
import '../../routes/routes.dart';
import '../../widgets/admin/order_status_chip.dart';

class AdminDashboardScreen extends ConsumerWidget {
  const AdminDashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authServiceProvider);
    final user = authState.user;
    final theme = Theme.of(context);

    if (user == null || !user.isAdminUser) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Access Denied'),
          backgroundColor: theme.primaryColor,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.lock, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'You do not have permission to access this area.',
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Dashboard'),
        backgroundColor: theme.primaryColor,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // TODO: Show notifications
            },
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.account_circle),
            onSelected: (action) => _handleMenuAction(context, ref, action),
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'profile',
                    child: Row(
                      children: [
                        const Icon(Icons.person, size: 20),
                        const SizedBox(width: 8),
                        Text('Profile (${user.name})'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'settings',
                    child: Row(
                      children: [
                        Icon(Icons.settings, size: 20),
                        SizedBox(width: 8),
                        Text('Settings'),
                      ],
                    ),
                  ),
                  const PopupMenuDivider(),
                  const PopupMenuItem(
                    value: 'logout',
                    child: Row(
                      children: [
                        Icon(Icons.logout, size: 20, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Logout', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Card
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: theme.primaryColor,
                          child: Text(
                            user.name.isNotEmpty
                                ? user.name[0].toUpperCase()
                                : 'A',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Welcome back, ${user.name}!',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                'Role: ${user.role.name.toUpperCase()}',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Real-time Stats Cards
            Text(
              'Real-time Overview',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: theme.primaryColor,
              ),
            ),
            const SizedBox(height: 12),

            _buildRealTimeStats(),

            const SizedBox(height: 24),

            // Quick Actions
            Text(
              'Quick Actions',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: theme.primaryColor,
              ),
            ),
            const SizedBox(height: 12),

            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.5,
              children: [
                if (user.hasPermission(Permission.viewOrders))
                  _buildActionCard(
                    context,
                    'View Orders',
                    Icons.list_alt,
                    Colors.orange,
                    () => _navigateToOrders(context, null),
                  ),
                if (user.hasPermission(Permission.viewMenu))
                  _buildActionCard(
                    context,
                    'Manage Menu',
                    Icons.restaurant_menu,
                    Colors.blue,
                    () => _navigateToMenu(context),
                  ),
                if (user.hasPermission(Permission.viewAnalytics))
                  _buildActionCard(
                    context,
                    'View Analytics',
                    Icons.analytics,
                    Colors.green,
                    () => _navigateToAnalytics(context),
                  ),
                if (user.hasPermission(Permission.editRestaurantSettings))
                  _buildActionCard(
                    context,
                    'Settings',
                    Icons.settings,
                    Colors.grey,
                    () => _navigateToSettings(context),
                  ),
              ],
            ),

            const SizedBox(height: 24),

            // Recent Orders with Quick Actions
            Text(
              'Recent Orders',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: theme.primaryColor,
              ),
            ),
            const SizedBox(height: 12),

            _buildRecentOrders(),

            const SizedBox(height: 24),

            // Urgent Orders Alert
            _buildUrgentOrdersAlert(),
          ],
        ),
      ),
    );
  }

  Widget _buildActionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Real-time stats widget
  Widget _buildRealTimeStats() {
    return Consumer(
      builder: (context, ref, child) {
        final realTimeMetricsAsync = ref.watch(realTimeMetricsProvider);

        return realTimeMetricsAsync.when(
          data:
              (metrics) => Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          context,
                          'Pending Orders',
                          '${metrics['pendingOrders']}',
                          Icons.pending_actions,
                          Colors.orange,
                          onTap:
                              () => _navigateToOrders(
                                context,
                                OrderStatus.pending,
                              ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          context,
                          'Today\'s Revenue',
                          'TZS ${(metrics['todayRevenue'] as double).toStringAsFixed(0)}',
                          Icons.attach_money,
                          Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          context,
                          'Today\'s Orders',
                          '${metrics['todayOrders']}',
                          Icons.shopping_cart,
                          Colors.blue,
                          onTap: () => _navigateToOrders(context, null),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          context,
                          'Active Items',
                          '${metrics['activeMenuItems']}',
                          Icons.restaurant_menu,
                          Colors.purple,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
          loading: () => const Center(child: CircularProgressIndicator()),
          error:
              (error, stack) => Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text('Error loading stats: $error'),
                ),
              ),
        );
      },
    );
  }

  // Recent orders widget
  Widget _buildRecentOrders() {
    return Consumer(
      builder: (context, ref, child) {
        final todayOrdersAsync = ref.watch(todayOrdersStreamProvider);

        return todayOrdersAsync.when(
          data: (orders) {
            final recentOrders = orders.take(5).toList();

            if (recentOrders.isEmpty) {
              return Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      Icon(Icons.inbox, size: 48, color: Colors.grey[400]),
                      const SizedBox(height: 16),
                      Text(
                        'No recent orders',
                        style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              );
            }

            return Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Latest Orders',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        TextButton(
                          onPressed: () => _navigateToOrders(context, null),
                          child: const Text('View All'),
                        ),
                      ],
                    ),
                  ),
                  ...recentOrders.map(
                    (order) => _buildOrderListItem(context, order),
                  ),
                ],
              ),
            );
          },
          loading:
              () => const Card(
                child: Padding(
                  padding: EdgeInsets.all(24),
                  child: Center(child: CircularProgressIndicator()),
                ),
              ),
          error:
              (error, stack) => Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text('Error loading orders: $error'),
                ),
              ),
        );
      },
    );
  }

  // Urgent orders alert widget
  Widget _buildUrgentOrdersAlert() {
    return Consumer(
      builder: (context, ref, child) {
        final pendingOrdersAsync = ref.watch(pendingOrdersStreamProvider);

        return pendingOrdersAsync.when(
          data: (orders) {
            final urgentOrders =
                orders.where((order) {
                  final timeSinceOrder = DateTime.now().difference(
                    order.createdAt,
                  );
                  return timeSinceOrder.inMinutes >
                      15; // Orders older than 15 minutes
                }).toList();

            if (urgentOrders.isEmpty) {
              return const SizedBox.shrink();
            }

            return Card(
              elevation: 2,
              color: Colors.red[50],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.warning, color: Colors.red[700], size: 24),
                        const SizedBox(width: 8),
                        Text(
                          'Urgent Orders (${urgentOrders.length})',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.red[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'These orders have been pending for more than 15 minutes',
                      style: TextStyle(color: Colors.red[600]),
                    ),
                    const SizedBox(height: 12),
                    ElevatedButton.icon(
                      onPressed:
                          () => _navigateToOrders(context, OrderStatus.pending),
                      icon: const Icon(Icons.visibility),
                      label: const Text('View Pending Orders'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red[700],
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
          loading: () => const SizedBox.shrink(),
          error: (error, stack) => const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildOrderListItem(BuildContext context, OrderModel order) {
    final timeSinceOrder = DateTime.now().difference(order.createdAt);
    final isUrgent =
        order.status == OrderStatus.pending && timeSinceOrder.inMinutes > 15;

    return Consumer(
      builder: (context, ref, child) {
        return Container(
          decoration: BoxDecoration(
            border: Border(top: BorderSide(color: Colors.grey[200]!)),
          ),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor:
                  isUrgent
                      ? Colors.red[100]
                      : _getStatusColor(order.status).withValues(alpha: 0.1),
              child: Icon(
                _getStatusIcon(order.status),
                color:
                    isUrgent ? Colors.red[700] : _getStatusColor(order.status),
                size: 20,
              ),
            ),
            title: Text(
              'Order #${order.id.substring(0, 8)}',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'TZS ${order.total.toStringAsFixed(0)} • ${order.items.length} items',
                ),
                Text(
                  _formatTimeAgo(timeSinceOrder),
                  style: TextStyle(
                    color: isUrgent ? Colors.red[600] : Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                OrderStatusChip(status: order.status),
                const SizedBox(width: 8),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, size: 20),
                  onSelected:
                      (value) => _handleOrderAction(context, ref, order, value),
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem(
                          value: 'view',
                          child: Row(
                            children: [
                              Icon(Icons.visibility, size: 16),
                              SizedBox(width: 8),
                              Text('View Details'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'update_status',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 16),
                              SizedBox(width: 8),
                              Text('Update Status'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'assign',
                          child: Row(
                            children: [
                              Icon(Icons.person_add, size: 16),
                              SizedBox(width: 8),
                              Text('Assign to Me'),
                            ],
                          ),
                        ),
                      ],
                ),
              ],
            ),
            onTap: () => _navigateToOrderDetails(context, order.id),
          ),
        );
      },
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color, {
    VoidCallback? onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Icon(icon, color: color, size: 24),
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods
  void _navigateToOrders(BuildContext context, OrderStatus? status) {
    // Navigate to orders screen with optional status filter
    context.goNamed(AppRoute.adminOrders.name, extra: status);
  }

  void _navigateToOrderDetails(BuildContext context, String orderId) {
    context.goNamed(
      AppRoute.adminOrderDetails.name,
      pathParameters: {'orderId': orderId},
    );
  }

  void _navigateToMenu(BuildContext context) {
    context.goNamed(AppRoute.adminMenu.name);
  }

  void _navigateToAnalytics(BuildContext context) {
    context.goNamed(AppRoute.adminAnalytics.name);
  }

  void _navigateToSettings(BuildContext context) {
    context.goNamed(AppRoute.adminSettings.name);
  }

  void _handleMenuAction(BuildContext context, WidgetRef ref, String action) {
    switch (action) {
      case 'profile':
        _showProfileDialog(context, ref);
        break;
      case 'settings':
        _navigateToSettings(context);
        break;
      case 'logout':
        _showLogoutConfirmation(context, ref);
        break;
    }
  }

  void _showProfileDialog(BuildContext context, WidgetRef ref) {
    final user = ref.read(authServiceProvider).user;
    if (user == null) return;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Profile Information'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildProfileRow('Name', user.name),
                _buildProfileRow('Email', user.email),
                _buildProfileRow('Role', user.role.name.toUpperCase()),
                _buildProfileRow(
                  'Status',
                  user.isActive ? 'Active' : 'Inactive',
                ),
                if (user.restaurantId != null)
                  _buildProfileRow('Restaurant ID', user.restaurantId!),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  Widget _buildProfileRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _showLogoutConfirmation(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirm Logout'),
            content: const Text('Are you sure you want to logout?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  await _performLogout(context, ref);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Logout'),
              ),
            ],
          ),
    );
  }

  Future<void> _performLogout(BuildContext context, WidgetRef ref) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => const AlertDialog(
              content: Row(
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 16),
                  Text('Logging out...'),
                ],
              ),
            ),
      );

      // Perform logout
      await ref.read(authServiceProvider.notifier).signOut();

      // Close loading dialog and navigate to login
      if (context.mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        context.go('/login');
      }
    } catch (e) {
      // Close loading dialog if it's open
      if (context.mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to logout: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleOrderAction(
    BuildContext context,
    WidgetRef ref,
    OrderModel order,
    String action,
  ) {
    switch (action) {
      case 'view':
        _navigateToOrderDetails(context, order.id);
        break;
      case 'update_status':
        _showStatusUpdateDialog(context, ref, order);
        break;
      case 'assign':
        _assignOrderToMe(context, ref, order);
        break;
    }
  }

  void _showStatusUpdateDialog(
    BuildContext context,
    WidgetRef ref,
    OrderModel order,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => OrderStatusUpdateDialog(
            order: order,
            onUpdate:
                (newStatus, note) =>
                    _updateOrderStatus(context, ref, order, newStatus, note),
          ),
    );
  }

  Future<void> _updateOrderStatus(
    BuildContext context,
    WidgetRef ref,
    OrderModel order,
    OrderStatus newStatus,
    String? note,
  ) async {
    final user = ref.read(authServiceProvider).user;
    if (user == null) return;

    try {
      await ref
          .read(adminOrderServiceProvider)
          .updateOrderStatus(
            orderId: order.id,
            newStatus: newStatus,
            staffId: user.uid,
            note: note,
          );

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Order status updated to ${newStatus.displayName}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update order: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _assignOrderToMe(
    BuildContext context,
    WidgetRef ref,
    OrderModel order,
  ) async {
    final user = ref.read(authServiceProvider).user;
    if (user == null) return;

    try {
      await ref
          .read(adminOrderServiceProvider)
          .assignOrderToStaff(
            orderId: order.id,
            staffId: user.uid,
            note: 'Self-assigned from dashboard',
          );

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Order assigned to you'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to assign order: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.confirmed:
        return Colors.blue;
      case OrderStatus.preparing:
        return Colors.amber;
      case OrderStatus.ready:
        return Colors.green;
      case OrderStatus.outForDelivery:
        return Colors.indigo;
      case OrderStatus.delivered:
        return Colors.teal;
      case OrderStatus.completed:
        return Colors.green;
      case OrderStatus.cancelled:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Icons.pending_actions;
      case OrderStatus.confirmed:
        return Icons.check_circle_outline;
      case OrderStatus.preparing:
        return Icons.restaurant;
      case OrderStatus.ready:
        return Icons.done_all;
      case OrderStatus.outForDelivery:
        return Icons.delivery_dining;
      case OrderStatus.delivered:
        return Icons.check_circle;
      case OrderStatus.completed:
        return Icons.verified;
      case OrderStatus.cancelled:
        return Icons.cancel;
    }
  }

  String _formatTimeAgo(Duration duration) {
    if (duration.inMinutes < 1) {
      return 'Just now';
    } else if (duration.inMinutes < 60) {
      return '${duration.inMinutes}m ago';
    } else if (duration.inHours < 24) {
      return '${duration.inHours}h ago';
    } else {
      return '${duration.inDays}d ago';
    }
  }
}
