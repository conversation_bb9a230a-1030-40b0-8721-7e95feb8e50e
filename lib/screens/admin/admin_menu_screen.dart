import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/menu_item.dart';
import '../../services/admin_menu_service.dart';
import '../../services/auth_service.dart';
import '../../widgets/admin/admin_menu_item_card.dart';
import '../../widgets/admin/menu_item_form.dart';

class AdminMenuScreen extends ConsumerStatefulWidget {
  const AdminMenuScreen({super.key});

  @override
  ConsumerState<AdminMenuScreen> createState() => _AdminMenuScreenState();
}

class _AdminMenuScreenState extends ConsumerState<AdminMenuScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String? _selectedCategoryFilter;
  String _searchQuery = '';
  final _searchController = TextEditingController();
  List<String> _selectedItemIds = [];
  bool _isSelectionMode = false;
  bool _showAvailableOnly = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authServiceProvider);
    final user = authState.user;
    final theme = Theme.of(context);

    if (user == null || !user.isAdminUser) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Access Denied'),
          backgroundColor: theme.primaryColor,
        ),
        body: const Center(
          child: Text('You do not have permission to access this area.'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Menu Management'),
        backgroundColor: theme.primaryColor,
        actions: [
          if (_isSelectionMode) ...[
            IconButton(
              icon: const Icon(Icons.select_all),
              onPressed: _selectAll,
              tooltip: 'Select All',
            ),
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: _clearSelection,
              tooltip: 'Clear Selection',
            ),
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert),
              tooltip: 'Bulk Actions',
              onSelected: _handleBulkAction,
              itemBuilder:
                  (context) => [
                    const PopupMenuItem(
                      value: 'enable',
                      child: Row(
                        children: [
                          Icon(Icons.check_circle, color: Colors.green),
                          SizedBox(width: 8),
                          Text('Enable Selected'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'disable',
                      child: Row(
                        children: [
                          Icon(Icons.cancel, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Disable Selected'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete Selected'),
                        ],
                      ),
                    ),
                  ],
            ),
          ] else ...[
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: _showSearchDialog,
              tooltip: 'Search Menu Items',
            ),
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: _showFilterDialog,
              tooltip: 'Filter Menu Items',
            ),
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: _addNewMenuItem,
              tooltip: 'Add Menu Item',
            ),
          ],
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          tabs: const [
            Tab(text: 'All Items'),
            Tab(text: 'Available'),
            Tab(text: 'Statistics'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllItemsTab(),
          _buildAvailableItemsTab(),
          _buildStatisticsTab(),
        ],
      ),
      floatingActionButton:
          _isSelectionMode
              ? null
              : FloatingActionButton(
                onPressed: _addNewMenuItem,
                child: const Icon(Icons.add),
              ),
    );
  }

  Widget _buildAllItemsTab() {
    final itemsAsync = ref.watch(allMenuItemsStreamProvider);

    return itemsAsync.when(
      data: (items) => _buildMenuItemsList(items),
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text('Error loading menu items: $error'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => ref.refresh(allMenuItemsStreamProvider),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildAvailableItemsTab() {
    final itemsAsync = ref.watch(availableMenuItemsStreamProvider);

    return itemsAsync.when(
      data: (items) => _buildMenuItemsList(items),
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stack) =>
              Center(child: Text('Error loading available items: $error')),
    );
  }

  Widget _buildStatisticsTab() {
    final statsAsync = ref.watch(menuStatisticsProvider);

    return statsAsync.when(
      data: (stats) => _buildStatisticsView(stats),
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stack) =>
              Center(child: Text('Error loading statistics: $error')),
    );
  }

  Widget _buildMenuItemsList(List<MenuItem> allItems) {
    // Apply filters
    List<MenuItem> filteredItems = allItems;

    if (_selectedCategoryFilter != null) {
      filteredItems =
          filteredItems
              .where((item) => item.category == _selectedCategoryFilter)
              .toList();
    }

    if (_showAvailableOnly) {
      filteredItems = filteredItems.where((item) => item.isAvailable).toList();
    }

    if (_searchQuery.isNotEmpty) {
      filteredItems =
          filteredItems.where((item) {
            return item.name.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                item.description.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                item.category.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                );
          }).toList();
    }

    if (filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.restaurant_menu, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty || _selectedCategoryFilter != null
                  ? 'No menu items match your filters'
                  : 'No menu items found',
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
            ),
            if (_searchQuery.isNotEmpty || _selectedCategoryFilter != null) ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _clearFilters,
                child: const Text('Clear Filters'),
              ),
            ],
          ],
        ),
      );
    }

    return Column(
      children: [
        // Filter chips
        if (_selectedCategoryFilter != null ||
            _searchQuery.isNotEmpty ||
            _showAvailableOnly)
          Container(
            padding: const EdgeInsets.all(16),
            child: Wrap(
              spacing: 8,
              children: [
                if (_selectedCategoryFilter != null)
                  Chip(
                    label: Text('Category: $_selectedCategoryFilter'),
                    onDeleted: () {
                      setState(() {
                        _selectedCategoryFilter = null;
                      });
                    },
                  ),
                if (_searchQuery.isNotEmpty)
                  Chip(
                    label: Text('Search: $_searchQuery'),
                    onDeleted: () {
                      setState(() {
                        _searchQuery = '';
                        _searchController.clear();
                      });
                    },
                  ),
                if (_showAvailableOnly)
                  Chip(
                    label: const Text('Available Only'),
                    onDeleted: () {
                      setState(() {
                        _showAvailableOnly = false;
                      });
                    },
                  ),
              ],
            ),
          ),

        // Items list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredItems.length,
            itemBuilder: (context, index) {
              final item = filteredItems[index];
              final isSelected = _selectedItemIds.contains(item.id);

              return AdminMenuItemCard(
                item: item,
                isSelected: isSelected,
                onTap:
                    _isSelectionMode
                        ? () => _toggleItemSelection(item.id)
                        : null,
                onLongPress: () => _toggleSelectionMode(item.id),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStatisticsView(Map<String, dynamic> stats) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Overview Cards
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Items',
                  '${stats['totalItems']}',
                  Icons.restaurant_menu,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Available Items',
                  '${stats['availableItems']}',
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Unavailable Items',
                  '${stats['unavailableItems']}',
                  Icons.cancel,
                  Colors.red,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Avg Price',
                  'TZS ${(stats['averagePrice'] as double).toStringAsFixed(0)}',
                  Icons.attach_money,
                  Colors.orange,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Category Breakdown
          const Text(
            'Category Breakdown',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children:
                    (stats['categoryStats'] as Map<String, int>).entries.map((
                      entry,
                    ) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(entry.key),
                            Text(
                              '${entry.value}',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Price Range
          const Text(
            'Price Range',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Minimum Price'),
                      Text(
                        'TZS ${(stats['minPrice'] as double).toStringAsFixed(2)}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Maximum Price'),
                      Text(
                        'TZS ${(stats['maxPrice'] as double).toStringAsFixed(2)}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Average Price'),
                      Text(
                        'TZS ${(stats['averagePrice'] as double).toStringAsFixed(2)}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(icon, color: color, size: 24),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  // Selection and filter methods
  void _toggleSelectionMode(String itemId) {
    setState(() {
      if (_isSelectionMode) {
        _toggleItemSelection(itemId);
      } else {
        _isSelectionMode = true;
        _selectedItemIds = [itemId];
      }
    });
  }

  void _toggleItemSelection(String itemId) {
    setState(() {
      if (_selectedItemIds.contains(itemId)) {
        _selectedItemIds.remove(itemId);
        if (_selectedItemIds.isEmpty) {
          _isSelectionMode = false;
        }
      } else {
        _selectedItemIds.add(itemId);
      }
    });
  }

  void _selectAll() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Select All functionality coming soon')),
    );
  }

  void _clearSelection() {
    setState(() {
      _selectedItemIds.clear();
      _isSelectionMode = false;
    });
  }

  void _clearFilters() {
    setState(() {
      _selectedCategoryFilter = null;
      _searchQuery = '';
      _searchController.clear();
      _showAvailableOnly = false;
    });
  }

  Future<void> _handleBulkAction(String action) async {
    if (_selectedItemIds.isEmpty) return;

    try {
      switch (action) {
        case 'enable':
          await ref
              .read(adminMenuServiceProvider)
              .bulkUpdateAvailability(
                itemIds: _selectedItemIds,
                isAvailable: true,
              );
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Enabled ${_selectedItemIds.length} items'),
                backgroundColor: Colors.green,
              ),
            );
          }
          break;
        case 'disable':
          await ref
              .read(adminMenuServiceProvider)
              .bulkUpdateAvailability(
                itemIds: _selectedItemIds,
                isAvailable: false,
              );
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Disabled ${_selectedItemIds.length} items'),
                backgroundColor: Colors.orange,
              ),
            );
          }
          break;
        case 'delete':
          final confirmed = await _confirmBulkDelete();
          if (confirmed) {
            for (final itemId in _selectedItemIds) {
              await ref.read(adminMenuServiceProvider).deleteMenuItem(itemId);
            }
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Deleted ${_selectedItemIds.length} items'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
          break;
      }
      _clearSelection();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to perform bulk action: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<bool> _confirmBulkDelete() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Items'),
            content: Text(
              'Are you sure you want to delete ${_selectedItemIds.length} selected items? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
    return confirmed ?? false;
  }

  void _addNewMenuItem() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => MenuItemForm(
              onSaved: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Menu item created successfully'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
            ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Search Menu Items'),
            content: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Enter item name, description, or category...',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _searchQuery = _searchController.text;
                  });
                  Navigator.pop(context);
                },
                child: const Text('Search'),
              ),
            ],
          ),
    );
  }

  void _showFilterDialog() {
    final categoriesAsync = ref.read(menuCategoriesProvider);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Filter Menu Items'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Filter by Category:'),
                  const SizedBox(height: 8),
                  categoriesAsync.when(
                    data:
                        (categories) => Wrap(
                          spacing: 8,
                          children: [
                            FilterChip(
                              label: const Text('All'),
                              selected: _selectedCategoryFilter == null,
                              onSelected: (selected) {
                                setState(() {
                                  _selectedCategoryFilter = null;
                                });
                                Navigator.pop(context);
                              },
                            ),
                            ...categories.map((category) {
                              return FilterChip(
                                label: Text(category),
                                selected: _selectedCategoryFilter == category,
                                onSelected: (selected) {
                                  setState(() {
                                    _selectedCategoryFilter =
                                        selected ? category : null;
                                  });
                                  Navigator.pop(context);
                                },
                              );
                            }),
                          ],
                        ),
                    loading: () => const CircularProgressIndicator(),
                    error: (error, stack) => Text('Error: $error'),
                  ),

                  const SizedBox(height: 16),

                  CheckboxListTile(
                    title: const Text('Available Items Only'),
                    value: _showAvailableOnly,
                    onChanged: (value) {
                      setState(() {
                        _showAvailableOnly = value ?? false;
                      });
                      Navigator.pop(context);
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }
}
