import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../services/analytics_service.dart';
import '../../services/auth_service.dart';
import '../../widgets/admin/analytics_charts.dart';
import '../../widgets/admin/analytics_metric_card.dart';

class AdminAnalyticsScreen extends ConsumerStatefulWidget {
  const AdminAnalyticsScreen({super.key});

  @override
  ConsumerState<AdminAnalyticsScreen> createState() =>
      _AdminAnalyticsScreenState();
}

class _AdminAnalyticsScreenState extends ConsumerState<AdminAnalyticsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _selectedPeriod = 'daily';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authServiceProvider);
    final user = authState.user;
    final theme = Theme.of(context);

    if (user == null || !user.isAdminUser) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Access Denied'),
          backgroundColor: theme.primaryColor,
        ),
        body: const Center(
          child: Text('You do not have permission to access this area.'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Analytics & Reports'),
        backgroundColor: theme.primaryColor,
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _showDateRangePicker,
            tooltip: 'Select Date Range',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
            tooltip: 'Refresh Data',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.download),
            tooltip: 'Export Reports',
            onSelected: _exportReport,
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'pdf',
                    child: Row(
                      children: [
                        Icon(Icons.picture_as_pdf),
                        SizedBox(width: 8),
                        Text('Export as PDF'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'csv',
                    child: Row(
                      children: [
                        Icon(Icons.table_chart),
                        SizedBox(width: 8),
                        Text('Export as CSV'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Sales'),
            Tab(text: 'Popular Items'),
            Tab(text: 'Customers'),
            Tab(text: 'Operations'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildSalesTab(),
          _buildPopularItemsTab(),
          _buildCustomersTab(),
          _buildOperationsTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    final realTimeMetricsAsync = ref.watch(realTimeMetricsProvider);
    final dashboardAnalyticsAsync = ref.watch(
      dashboardAnalyticsProvider({'start': _startDate, 'end': _endDate}),
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date range indicator
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.date_range, size: 16, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Period: ${DateFormat('MMM dd, yyyy').format(_startDate)} - ${DateFormat('MMM dd, yyyy').format(_endDate)}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Real-time metrics
          const Text(
            'Real-time Metrics',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),

          realTimeMetricsAsync.when(
            data: (metrics) => _buildRealTimeMetrics(metrics),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Text('Error: $error'),
          ),

          const SizedBox(height: 24),

          // Dashboard analytics
          const Text(
            'Period Analytics',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),

          dashboardAnalyticsAsync.when(
            data: (analytics) => _buildDashboardAnalytics(analytics),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Text('Error: $error'),
          ),
        ],
      ),
    );
  }

  Widget _buildRealTimeMetrics(Map<String, dynamic> metrics) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      childAspectRatio: 1.5,
      children: [
        AnalyticsMetricCard(
          title: 'Today\'s Orders',
          value: '${metrics['todayOrders']}',
          icon: Icons.shopping_cart,
          color: Colors.blue,
          subtitle: 'Orders placed today',
        ),
        AnalyticsMetricCard(
          title: 'Today\'s Revenue',
          value: _formatCurrency(metrics['todayRevenue'] as double),
          icon: Icons.attach_money,
          color: Colors.green,
          subtitle: 'Revenue generated today',
        ),
        AnalyticsMetricCard(
          title: 'Pending Orders',
          value: '${metrics['pendingOrders']}',
          icon: Icons.pending_actions,
          color: Colors.orange,
          subtitle: 'Awaiting processing',
        ),
        AnalyticsMetricCard(
          title: 'Active Menu Items',
          value: '${metrics['activeMenuItems']}',
          icon: Icons.restaurant_menu,
          color: Colors.purple,
          subtitle: 'Available menu items',
        ),
      ],
    );
  }

  Widget _buildDashboardAnalytics(Map<String, dynamic> analytics) {
    final overview = analytics['overview'] as Map<String, dynamic>;
    final trends = analytics['trends'] as Map<String, dynamic>;
    final distributions = analytics['distributions'] as Map<String, dynamic>;

    return Column(
      children: [
        // Overview metrics
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            AnalyticsMetricCard(
              title: 'Total Orders',
              value: '${overview['totalOrders']}',
              icon: Icons.list_alt,
              color: Colors.blue,
              subtitle: 'Orders in period',
            ),
            AnalyticsMetricCard(
              title: 'Total Revenue',
              value:
                  'TZS ${(overview['totalRevenue'] as double).toStringAsFixed(0)}',
              icon: Icons.trending_up,
              color: Colors.green,
              subtitle: 'Revenue in period',
            ),
            AnalyticsMetricCard(
              title: 'Average Order Value',
              value:
                  'TZS ${(overview['averageOrderValue'] as double).toStringAsFixed(0)}',
              icon: Icons.receipt,
              color: Colors.orange,
              subtitle: 'Per order average',
            ),
            AnalyticsMetricCard(
              title: 'Conversion Rate',
              value:
                  '${(overview['conversionRate'] as double).toStringAsFixed(1)}%',
              icon: Icons.percent,
              color: Colors.purple,
              subtitle: 'Order completion rate',
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Charts
        SimpleLineChart(
          title: 'Daily Revenue Trend',
          data: Map<String, double>.from(
            (trends['dailyRevenue'] as Map<String, dynamic>).map(
              (key, value) => MapEntry(key, (value as num).toDouble()),
            ),
          ),
          color: Colors.green,
          valuePrefix: 'TZS ',
        ),

        const SizedBox(height: 16),

        SimplePieChart(
          title: 'Order Status Distribution',
          data: Map<String, double>.from(
            (distributions['orderStatus'] as Map<String, dynamic>).map(
              (key, value) => MapEntry(key, (value as num).toDouble()),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSalesTab() {
    final salesAnalyticsAsync = ref.watch(
      salesAnalyticsProvider({
        'startDate': _startDate,
        'endDate': _endDate,
        'period': _selectedPeriod,
      }),
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Period selector
          Row(
            children: [
              const Text('Period: '),
              DropdownButton<String>(
                value: _selectedPeriod,
                items: const [
                  DropdownMenuItem(value: 'daily', child: Text('Daily')),
                  DropdownMenuItem(value: 'weekly', child: Text('Weekly')),
                  DropdownMenuItem(value: 'monthly', child: Text('Monthly')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedPeriod = value ?? 'daily';
                  });
                },
              ),
            ],
          ),

          const SizedBox(height: 16),

          salesAnalyticsAsync.when(
            data: (analytics) => _buildSalesAnalytics(analytics),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Text('Error: $error'),
          ),
        ],
      ),
    );
  }

  Widget _buildSalesAnalytics(Map<String, dynamic> analytics) {
    final summary = analytics['summary'] as Map<String, dynamic>;
    final timeSeries = analytics['timeSeries'] as Map<String, dynamic>;

    return Column(
      children: [
        // Summary cards
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            AnalyticsMetricCard(
              title: 'Total Revenue',
              value:
                  'TZS ${(summary['totalRevenue'] as double).toStringAsFixed(0)}',
              icon: Icons.attach_money,
              color: Colors.green,
            ),
            AnalyticsMetricCard(
              title: 'Total Orders',
              value: '${summary['totalOrders']}',
              icon: Icons.shopping_cart,
              color: Colors.blue,
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Time series chart
        SimpleLineChart(
          title: 'Revenue Trend (${summary['period']})',
          data: Map<String, double>.from(
            timeSeries.map(
              (key, value) => MapEntry(
                key,
                (value as Map<String, dynamic>)['revenue'] as double,
              ),
            ),
          ),
          color: Colors.green,
          valuePrefix: 'TZS ',
        ),

        const SizedBox(height: 16),

        SimpleBarChart(
          title: 'Orders Trend (${summary['period']})',
          data: Map<String, double>.from(
            timeSeries.map(
              (key, value) => MapEntry(
                key,
                ((value as Map<String, dynamic>)['orders'] as int).toDouble(),
              ),
            ),
          ),
          color: Colors.blue,
        ),
      ],
    );
  }

  Widget _buildPopularItemsTab() {
    final popularItemsAsync = ref.watch(
      popularItemsProvider({
        'startDate': _startDate,
        'endDate': _endDate,
        'limit': 10,
      }),
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: popularItemsAsync.when(
        data: (analytics) => _buildPopularItemsAnalytics(analytics),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Text('Error: $error'),
      ),
    );
  }

  Widget _buildPopularItemsAnalytics(Map<String, dynamic> analytics) {
    final topItems = analytics['topItems'] as List<dynamic>;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Top Selling Items',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),

        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: topItems.length,
          itemBuilder: (context, index) {
            final item = topItems[index] as Map<String, dynamic>;
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: Colors.blue,
                  child: Text('${index + 1}'),
                ),
                title: Text(item['name'] as String),
                subtitle: Text(
                  'Sold ${item['totalQuantity']} times in ${item['orderCount']} orders',
                ),
                trailing: Text(
                  'TZS ${(item['totalRevenue'] as double).toStringAsFixed(0)}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildCustomersTab() {
    final customerAnalyticsAsync = ref.watch(
      customerAnalyticsProvider({'start': _startDate, 'end': _endDate}),
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: customerAnalyticsAsync.when(
        data: (analytics) => _buildCustomerAnalytics(analytics),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Text('Error: $error'),
      ),
    );
  }

  Widget _buildCustomerAnalytics(Map<String, dynamic> analytics) {
    final summary = analytics['summary'] as Map<String, dynamic>;
    final topCustomers = analytics['topCustomers'] as List<dynamic>;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Customer summary
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            AnalyticsMetricCard(
              title: 'Total Customers',
              value: '${summary['totalCustomers']}',
              icon: Icons.people,
              color: Colors.blue,
            ),
            AnalyticsMetricCard(
              title: 'New Customers',
              value: '${summary['newCustomers']}',
              icon: Icons.person_add,
              color: Colors.green,
            ),
            AnalyticsMetricCard(
              title: 'Returning Customers',
              value: '${summary['returningCustomers']}',
              icon: Icons.repeat,
              color: Colors.orange,
            ),
            AnalyticsMetricCard(
              title: 'Retention Rate',
              value:
                  '${(summary['customerRetentionRate'] as double).toStringAsFixed(1)}%',
              icon: Icons.trending_up,
              color: Colors.purple,
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Top customers
        const Text(
          'Top Customers',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),

        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: topCustomers.length,
          itemBuilder: (context, index) {
            final customer = topCustomers[index] as Map<String, dynamic>;
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: Colors.purple,
                  child: Text(
                    (customer['name'] as String).isNotEmpty
                        ? (customer['name'] as String)[0].toUpperCase()
                        : 'U',
                  ),
                ),
                title: Text(customer['name'] as String),
                subtitle: Text(
                  '${customer['totalOrders']} orders • Avg: TZS ${(customer['averageOrderValue'] as double).toStringAsFixed(0)}',
                ),
                trailing: Text(
                  'TZS ${(customer['totalSpent'] as double).toStringAsFixed(0)}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildOperationsTab() {
    final operationalAnalyticsAsync = ref.watch(
      operationalAnalyticsProvider({'start': _startDate, 'end': _endDate}),
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: operationalAnalyticsAsync.when(
        data: (analytics) => _buildOperationalAnalytics(analytics),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Text('Error: $error'),
      ),
    );
  }

  Widget _buildOperationalAnalytics(Map<String, dynamic> analytics) {
    final hourlyDistribution =
        analytics['hourlyDistribution'] as Map<String, dynamic>;
    final dayOfWeekDistribution =
        analytics['dayOfWeekDistribution'] as Map<String, dynamic>;
    final peakHours = analytics['peakHours'] as List<dynamic>;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Peak hours
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Peak Hours',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  'Busiest hours: ${peakHours.map((h) => '$h:00').join(', ')}',
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Hourly distribution
        SimpleBarChart(
          title: 'Orders by Hour',
          data: Map<String, double>.from(
            hourlyDistribution.map(
              (key, value) => MapEntry('${key}h', (value as num).toDouble()),
            ),
          ),
          color: Colors.blue,
        ),

        const SizedBox(height: 16),

        // Day of week distribution
        SimpleBarChart(
          title: 'Orders by Day of Week',
          data: Map<String, double>.from(
            dayOfWeekDistribution.map(
              (key, value) => MapEntry(
                _getDayName(int.parse(key)),
                (value as num).toDouble(),
              ),
            ),
          ),
          color: Colors.green,
        ),
      ],
    );
  }

  String _getDayName(int dayOfWeek) {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return days[dayOfWeek - 1];
  }

  // Helper methods
  void _showDateRangePicker() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
    }
  }

  void _refreshData() {
    // Refresh all providers
    ref.invalidate(realTimeMetricsProvider);
    ref.invalidate(dashboardAnalyticsProvider);
    ref.invalidate(salesAnalyticsProvider);
    ref.invalidate(popularItemsProvider);
    ref.invalidate(customerAnalyticsProvider);
    ref.invalidate(operationalAnalyticsProvider);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Analytics data refreshed'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _exportReport(String format) {
    // TODO: Implement report export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Export as ${format.toUpperCase()} - Coming Soon'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  String _formatCurrency(double amount) {
    if (amount >= 1000000) {
      return 'TZS ${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return 'TZS ${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return 'TZS ${amount.toStringAsFixed(0)}';
    }
  }
}
