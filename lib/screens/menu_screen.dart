import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:smart_restaurant_menu/services/recomendation_service.dart';
import '../models/menu_item.dart';
import '../services/auth_service.dart';
import '../services/database_service.dart';
import '../widgets/menu_item_card.dart';

class MenuScreen extends ConsumerStatefulWidget {
  const MenuScreen({super.key});

  @override
  ConsumerState createState() => _MenuScreenState();
}

class _MenuScreenState extends ConsumerState<MenuScreen> {
  List<MenuItem> _menuItems = [];
  List<MenuItem> _recommendedItems = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadMenu();
  }

  Future<void> _loadMenu() async {
    final menuItems = await ref.read(databaseServiceProvider).getMenuItems();
    final user = ref.read(authServiceProvider).user;
    final recommendedItems =
        user != null
            ? ref
                .read(recommendationServiceProvider)
                .getRecommendations(user, menuItems)
            : [];

    setState(() {
      _menuItems = menuItems;
      _recommendedItems = recommendedItems.cast<MenuItem>();
      _isLoading = false;
    });
  }

  final List<String> _categories = [
    'All',
    'Appetizer',
    'Main Course',
    'Dessert',
    'Beverage',
  ];
  int _selectedCategoryIndex = 0;
  void _onCategorySelected(int index) {
    setState(() {
      _selectedCategoryIndex = index;
    });
  }

  List<MenuItem> get _filteredMenuItems {
    if (_selectedCategoryIndex == 0) {
      return _menuItems;
    } else {
      return _menuItems
          .where((item) => item.category == _categories[_selectedCategoryIndex])
          .toList();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).primaryColor,
        title: Text('Menu'),
        actions: [IconButton(onPressed: () {}, icon: Icon(Icons.search))],
      ),
      body:
          _isLoading
              ? Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 16, top: 16, bottom: 4),
                      child: Text(
                        'Recommended',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    _recommendedItems.isEmpty
                        ? Center(child: Text('No recommendations available'))
                        : SizedBox(
                          height: 200,
                          width: double.infinity,
                          child: ListView.builder(
                            shrinkWrap: true,
                            scrollDirection: Axis.horizontal,
                            itemCount: _recommendedItems.length,
                            itemBuilder: (context, index) {
                              return MenuItemCard(
                                item: _recommendedItems[index],
                              );
                            },
                          ),
                        ),
                    SizedBox(height: 16),
                    SizedBox(
                      height: 50,
                      width: double.infinity,
                      child: ListView.builder(
                        shrinkWrap: true,
                        scrollDirection: Axis.horizontal,
                        itemCount: _categories.length,
                        itemBuilder: (context, index) {
                          return TextButton(
                            onPressed: () => _onCategorySelected(index),
                            style: TextButton.styleFrom(
                              splashFactory: NoSplash.splashFactory,
                            ),
                            child: AnimatedDefaultTextStyle(
                              duration: Duration(milliseconds: 250),
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color:
                                    _selectedCategoryIndex == index
                                        ? Theme.of(context).primaryColor
                                        : Colors.black,
                              ),

                              child: Text(_categories[index]),
                            ),
                          );
                        },
                      ),
                    ),
                    GridView.builder(
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 0.9,
                      ),
                      itemCount: _filteredMenuItems.length,
                      itemBuilder: (context, index) {
                        return MenuItemCard(item: _filteredMenuItems[index]);
                      },
                    ),
                  ],
                ),
              ),
    );
  }
}
