import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:pay/pay.dart';
import '../services/payment_service.dart';
import '../services/clickpesa_service.dart';
import '../services/order_service.dart';
import '../services/auth_service.dart';
import '../providers/cart_provider.dart';
import '../models/order.dart';
import '../utils/validators.dart';
import '../utils/currency_formatter.dart';
import '../widgets/payment/clickpesa_payment_widget.dart';

class PaymentMethodScreen extends ConsumerStatefulWidget {
  final double totalAmount;
  final List<Map<String, dynamic>> orderItems;

  const PaymentMethodScreen({
    super.key,
    required this.totalAmount,
    required this.orderItems,
  });

  @override
  ConsumerState<PaymentMethodScreen> createState() =>
      _PaymentMethodScreenState();
}

class _PaymentMethodScreenState extends ConsumerState<PaymentMethodScreen> {
  PaymentMethodType _selectedPaymentMethod = PaymentMethodType.card;
  bool _isProcessingPayment = false;
  bool _applePayAvailable = false;
  bool _googlePayAvailable = false;
  List<Map<String, dynamic>> _savedPaymentMethods = [];

  @override
  void initState() {
    super.initState();
    _checkPaymentAvailability();
    _loadSavedPaymentMethods();
  }

  Future<void> _checkPaymentAvailability() async {
    final paymentService = ref.read(paymentServiceProvider);

    final applePayAvailable = await paymentService.isApplePayAvailable();
    final googlePayAvailable = await paymentService.isGooglePayAvailable();

    if (mounted) {
      setState(() {
        _applePayAvailable = applePayAvailable;
        _googlePayAvailable = googlePayAvailable;
      });
    }
  }

  Future<void> _loadSavedPaymentMethods() async {
    final paymentService = ref.read(paymentServiceProvider);
    final savedMethods = await paymentService.getSavedPaymentMethods();

    if (mounted) {
      setState(() {
        _savedPaymentMethods = savedMethods;
      });
    }
  }

  List<PaymentItem> _buildPaymentItems() {
    final items =
        widget.orderItems.map((item) {
          return PaymentItem(
            label: item['name'] ?? 'Item',
            amount: (item['totalPrice'] as double).toStringAsFixed(2),
            status: PaymentItemStatus.final_price,
          );
        }).toList();

    // Add total
    items.add(
      PaymentItem(
        label: 'Total',
        amount: widget.totalAmount.toStringAsFixed(2),
        status: PaymentItemStatus.final_price,
      ),
    );

    return items;
  }

  Future<void> _processPayment() async {
    if (_isProcessingPayment) return;

    setState(() {
      _isProcessingPayment = true;
    });

    try {
      final paymentService = ref.read(paymentServiceProvider);
      PaymentResult result;

      final metadata = {
        'order_items': widget.orderItems.length.toString(),
        'user_id': 'current_user_id', // Replace with actual user ID
        'timestamp': DateTime.now().toIso8601String(),
      };

      switch (_selectedPaymentMethod) {
        case PaymentMethodType.card:
          result = await paymentService.processCardPayment(
            amount: widget.totalAmount,
            currency: 'TZS',
            metadata: metadata,
          );
          break;

        case PaymentMethodType.applePay:
          result = await paymentService.processApplePayPayment(
            amount: widget.totalAmount,
            currency: 'TZS',
            paymentItems: _buildPaymentItems(),
            metadata: metadata,
          );
          break;

        case PaymentMethodType.googlePay:
          result = await paymentService.processGooglePayPayment(
            amount: widget.totalAmount,
            currency: 'TZS',
            paymentItems: _buildPaymentItems(),
            metadata: metadata,
          );
          break;

        case PaymentMethodType.clickPesa:
          // ClickPesa payment is handled differently - show payment widget
          _showClickPesaPayment();
          return;
      }

      if (mounted) {
        setState(() {
          _isProcessingPayment = false;
        });

        if (result.status == PaymentStatus.succeeded) {
          // Create order after successful payment
          await _createOrder(result.paymentIntentId);

          // Clear cart after successful payment
          ref.read(cartProvider.notifier).clearCart();

          // Navigate to success screen
          if (mounted) {
            context.pushReplacement(
              '/payment-success',
              extra: {
                'paymentIntentId': result.paymentIntentId,
                'amount': widget.totalAmount,
              },
            );
          }
        } else if (result.status == PaymentStatus.cancelled) {
          // User cancelled payment - do nothing
        } else {
          // Show error
          _showErrorDialog(result.error ?? 'Payment failed. Please try again.');
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isProcessingPayment = false;
        });
        _showErrorDialog('An unexpected error occurred. Please try again.');
      }
    }
  }

  Future<void> _createOrder(String? paymentIntentId) async {
    try {
      final authState = ref.read(authServiceProvider);
      final userId = authState.user?.uid;

      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Calculate order details
      final subtotal = widget.totalAmount * 0.85; // Assuming 15% is tax + fees
      final tax = widget.totalAmount * 0.10; // 10% tax
      final deliveryFee = widget.totalAmount * 0.05; // 5% delivery fee

      // Get user info for customerInfo
      final user = authState.user;
      final customerInfo =
          user != null
              ? {
                'name': user.name,
                'email': user.email,
                'phone': '', // TODO: Add phone field to user profile
                'address': '', // TODO: Add address field to checkout
                'notes': '', // TODO: Add notes field to checkout
              }
              : null;

      // Initialize status history with first entry
      final initialStatusHistory = [
        {
          'status': OrderStatus.pending.name,
          'timestamp': DateTime.now().toIso8601String(),
          'note': 'Order created and payment processed',
        },
      ];

      // Create order with unique ID
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final randomSuffix = (timestamp % 10000).toString().padLeft(4, '0');
      final order = OrderModel(
        id: 'order_${timestamp}_$randomSuffix',
        userId: userId,
        items: widget.orderItems,
        subtotal: subtotal,
        deliveryFee: deliveryFee,
        tax: tax,
        total: widget.totalAmount,
        status: OrderStatus.pending,
        orderType: OrderType.delivery, // Default to delivery for now
        createdAt: DateTime.now(),
        paymentIntentId: paymentIntentId,
        paymentMethod: _selectedPaymentMethod.name,
        estimatedDeliveryTime: ref
            .read(orderServiceProvider)
            .estimateDeliveryTime(OrderType.delivery),
        customerInfo: customerInfo,
        statusHistory: initialStatusHistory,
      );

      // Save order to Firebase
      await ref.read(orderServiceProvider).createOrder(order);
    } catch (e) {
      // Log error but don't prevent success flow - order creation failure shouldn't block payment success
      // In production, this would be logged to crashlytics or another logging service
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Payment Error'),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Method'),
        backgroundColor: theme.primaryColor,
      ),
      body: Column(
        children: [
          // Order summary
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Order Summary',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: theme.primaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${widget.orderItems.length} items',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Total Amount',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: theme.primaryColor,
                      ),
                    ),
                    Text(
                      '\$${widget.totalAmount.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.primaryColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Payment methods
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                Text(
                  'Select Payment Method',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: theme.primaryColor,
                  ),
                ),
                const SizedBox(height: 16),

                // Credit/Debit Card
                _buildPaymentMethodTile(
                  title: 'Credit or Debit Card',
                  subtitle: 'Visa, Mastercard, American Express',
                  icon: Icons.credit_card,
                  paymentMethod: PaymentMethodType.card,
                  isAvailable: true,
                ),

                // Apple Pay
                if (_applePayAvailable)
                  _buildPaymentMethodTile(
                    title: 'Apple Pay',
                    subtitle: 'Pay with Touch ID or Face ID',
                    icon: Icons.apple,
                    paymentMethod: PaymentMethodType.applePay,
                    isAvailable: true,
                  ),

                // Google Pay
                if (_googlePayAvailable)
                  _buildPaymentMethodTile(
                    title: 'Google Pay',
                    subtitle: 'Pay with your Google account',
                    icon: Icons.account_balance_wallet,
                    paymentMethod: PaymentMethodType.googlePay,
                    isAvailable: true,
                  ),

                // ClickPesa (Mobile Money)
                _buildPaymentMethodTile(
                  title: 'ClickPesa Mobile Money',
                  subtitle: 'M-Pesa, Tigo Pesa, Airtel Money, Halo Pesa',
                  icon: Icons.phone_android,
                  paymentMethod: PaymentMethodType.clickPesa,
                  isAvailable: true,
                ),

                // Saved payment methods
                if (_savedPaymentMethods.isNotEmpty) ...[
                  const SizedBox(height: 24),
                  Text(
                    'Saved Payment Methods',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: theme.primaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ..._savedPaymentMethods.map(
                    (method) => _buildSavedPaymentMethodTile(method),
                  ),
                ],
              ],
            ),
          ),

          // Pay button
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 5,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isProcessingPayment ? null : _processPayment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child:
                    _isProcessingPayment
                        ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                        : Text(
                          'Pay \$${widget.totalAmount.toStringAsFixed(2)}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required PaymentMethodType paymentMethod,
    required bool isAvailable,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: RadioListTile<PaymentMethodType>(
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: isAvailable ? null : Colors.grey,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(color: isAvailable ? Colors.grey[600] : Colors.grey),
        ),
        secondary: Icon(
          icon,
          color: isAvailable ? Theme.of(context).primaryColor : Colors.grey,
        ),
        value: paymentMethod,
        groupValue: _selectedPaymentMethod,
        onChanged:
            isAvailable
                ? (PaymentMethodType? value) {
                  if (value != null) {
                    setState(() {
                      _selectedPaymentMethod = value;
                    });
                  }
                }
                : null,
        activeColor: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildSavedPaymentMethodTile(Map<String, dynamic> method) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(Icons.credit_card, color: Theme.of(context).primaryColor),
        title: Text(
          '${method['brand']?.toString().toUpperCase() ?? 'Card'} •••• ${method['last4'] ?? ''}',
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          'Expires ${method['exp_month']}/${method['exp_year']}',
          style: TextStyle(color: Colors.grey[600]),
        ),
        trailing: IconButton(
          icon: const Icon(Icons.delete_outline),
          onPressed: () async {
            final confirmed = await showDialog<bool>(
              context: context,
              builder:
                  (context) => AlertDialog(
                    title: const Text('Remove Payment Method'),
                    content: const Text(
                      'Are you sure you want to remove this payment method?',
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        child: const Text('Remove'),
                      ),
                    ],
                  ),
            );

            if (confirmed == true) {
              await ref
                  .read(paymentServiceProvider)
                  .removePaymentMethod(method['id']);
              _loadSavedPaymentMethods();
            }
          },
        ),
      ),
    );
  }

  void _showClickPesaPayment() {
    final user = ref.read(authServiceProvider).user;
    if (user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please log in to continue')),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            height: MediaQuery.of(context).size.height * 0.8,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: ClickPesaPaymentWidget(
              amount: widget.totalAmount,
              orderId: DateTime.now().millisecondsSinceEpoch.toString(),
              customerName: user.name,
              customerEmail: user.email,
              onPaymentResult: (result) {
                Navigator.of(context).pop();
                _handleClickPesaPaymentResult(result);
              },
            ),
          ),
    );
  }

  void _handleClickPesaPaymentResult(ClickPesaPaymentResult result) async {
    if (result.isSuccess) {
      // Create order after successful payment
      await _createOrder(result.transactionId);

      // Clear cart after successful payment
      ref.read(cartProvider.notifier).clearCart();

      // Navigate to success screen with payment data
      if (mounted) {
        context.go(
          '/order-success',
          extra: {
            'transactionId': result.transactionId,
            'amount': widget.totalAmount,
            'paymentMethod': 'ClickPesa',
            'orderReference': result.orderReference,
          },
        );
      }
    } else {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment failed: ${result.message}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
