import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:smart_restaurant_menu/providers/cart_provider.dart';
import 'package:smart_restaurant_menu/widgets/custom_nav_bar.dart';

class ScaffoldWithNestedNavigation extends StatelessWidget {
  const ScaffoldWithNestedNavigation({Key? key, required this.navigationShell})
    : super(key: key ?? const ValueKey('ScaffoldWithNestedNavigation'));
  final StatefulNavigationShell navigationShell;

  void _goBranch(int index) {
    navigationShell.goBranch(
      index,
      initialLocation: index == navigationShell.currentIndex,
    );
  }

  @override
  Widget build(BuildContext context) {
    return ScaffoldWithNavigationBar(
      body: navigationShell,
      currentIndex: navigationShell.currentIndex,
      onDestinationSelected: _goBranch,
    );
  }
}

class ScaffoldWithNavigationBar extends ConsumerWidget {
  final Widget body;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;

  const ScaffoldWithNavigationBar({
    super.key,
    required this.body,
    required this.currentIndex,
    required this.onDestinationSelected,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final location = GoRouterState.of(context).fullPath;
    final cartItems = ref.watch(cartProvider);
    final cartItemCount = cartItems.length;

    print(" location $location");
    final List<String> hideNavBarPaths = ["/menu_item", "/menu_item/checkout"];
    final hideNavs = hideNavBarPaths.contains(location);
    return Scaffold(
      body: body,
      bottomNavigationBar:
          hideNavs
              ? null
              : CustomNavigationBar(
                selectedIndex: currentIndex,
                onMenuSelected: onDestinationSelected,
                menus: [
                  MenuItem(
                    activeIcon: Icon(
                      HugeIcons.strokeRoundedRestaurant02,
                      color: Colors.white,
                    ),
                    icon: Icon(HugeIcons.strokeRoundedRestaurant02),
                    label: 'Menu',
                  ),
                  MenuItem(
                    activeIcon: Icon(
                      HugeIcons.strokeRoundedOrganicFood,
                      color: Colors.white,
                    ),
                    icon: Icon(HugeIcons.strokeRoundedOrganicFood),
                    label: "Orders",
                  ),
                  MenuItem(
                    activeIcon: Stack(
                      children: [
                        Icon(
                          HugeIcons.strokeRoundedShoppingCart01,
                          color: Colors.white,
                        ),
                        if (cartItemCount > 0)
                          Positioned(
                            right: 0,
                            top: 0,
                            child: Container(
                              padding: EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              constraints: BoxConstraints(
                                minWidth: 16,
                                minHeight: 16,
                              ),
                              child: Text(
                                '$cartItemCount',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                      ],
                    ),
                    icon: Stack(
                      children: [
                        Icon(HugeIcons.strokeRoundedShoppingCart01),
                        if (cartItemCount > 0)
                          Positioned(
                            right: 0,
                            top: 0,
                            child: Container(
                              padding: EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              constraints: BoxConstraints(
                                minWidth: 16,
                                minHeight: 16,
                              ),
                              child: Text(
                                '$cartItemCount',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                      ],
                    ),
                    label: "Cart",
                  ),
                  MenuItem(
                    activeIcon: Icon(
                      HugeIcons.strokeRoundedUserAccount,
                      color: Colors.white,
                    ),
                    icon: Icon(HugeIcons.strokeRoundedUserAccount),
                    label: "Profile",
                  ),
                ],
              ),
    );
  }
}
