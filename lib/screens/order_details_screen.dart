import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../models/order.dart';
import '../models/user.dart';
import '../services/order_service.dart';
import '../services/admin_order_service.dart';
import '../services/auth_service.dart';
import '../services/permission_service.dart';
import '../widgets/admin/order_status_chip.dart';

class OrderDetailsScreen extends ConsumerStatefulWidget {
  final String orderId;

  const OrderDetailsScreen({super.key, required this.orderId});

  @override
  ConsumerState<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends ConsumerState<OrderDetailsScreen> {
  OrderModel? _order;
  bool _isLoading = true;
  bool _isUpdating = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadOrderDetails();
  }

  Future<void> _loadOrderDetails() async {
    try {
      final order = await ref
          .read(orderServiceProvider)
          .getOrder(widget.orderId);
      if (mounted) {
        setState(() {
          _order = order;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Order Details'),
          backgroundColor: theme.primaryColor,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_error != null || _order == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Order Details'),
          backgroundColor: theme.primaryColor,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                _error ?? 'Order not found',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      );
    }

    final order = _order!;
    final formattedDate = DateFormat(
      'MMM dd, yyyy • HH:mm',
    ).format(order.createdAt);

    // Parse hex color for status
    final statusColor = Color(
      int.parse(_getStatusColor(order.status).substring(1, 7), radix: 16) +
          0xFF000000,
    );

    // Check if user is admin
    final authState = ref.watch(authServiceProvider);
    final user = authState.user;
    final isAdmin = user?.isAdminUser ?? false;
    final canManageOrders = ref
        .read(permissionServiceProvider)
        .canManageOrders(user);

    return Scaffold(
      appBar: AppBar(
        title: Text('Order #${order.id.substring(0, 8)}'),
        backgroundColor: theme.primaryColor,
        actions:
            isAdmin && canManageOrders
                ? [
                  // Admin menu
                  PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert),
                    onSelected: (value) => _handleAdminAction(value, order),
                    itemBuilder:
                        (context) => [
                          const PopupMenuItem(
                            value: 'update_status',
                            child: Row(
                              children: [
                                Icon(Icons.edit, size: 20),
                                SizedBox(width: 8),
                                Text('Update Status'),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'assign_to_me',
                            child: Row(
                              children: [
                                Icon(Icons.person_add, size: 20),
                                SizedBox(width: 8),
                                Text('Assign to Me'),
                              ],
                            ),
                          ),
                          if (order.canBeCancelled)
                            const PopupMenuItem(
                              value: 'cancel_order',
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.cancel,
                                    size: 20,
                                    color: Colors.red,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    'Cancel Order',
                                    style: TextStyle(color: Colors.red),
                                  ),
                                ],
                              ),
                            ),
                          const PopupMenuItem(
                            value: 'view_analytics',
                            child: Row(
                              children: [
                                Icon(Icons.analytics, size: 20),
                                SizedBox(width: 8),
                                Text('View Analytics'),
                              ],
                            ),
                          ),
                        ],
                  ),
                ]
                : null,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Order Status Card
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Order Status',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: theme.primaryColor,
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: statusColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(color: statusColor, width: 1),
                            ),
                            child: Text(
                              order.statusDisplayName,
                              style: TextStyle(
                                color: statusColor,
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      _buildInfoRow(
                        Icons.calendar_today,
                        'Order Date',
                        formattedDate,
                      ),
                      _buildInfoRow(
                        Icons.delivery_dining,
                        'Order Type',
                        order.orderType.name.toUpperCase(),
                      ),
                      if (order.estimatedDeliveryTime != null)
                        _buildInfoRow(
                          Icons.access_time,
                          'Estimated Delivery',
                          DateFormat(
                            'HH:mm',
                          ).format(order.estimatedDeliveryTime!),
                        ),
                      if (order.paymentIntentId != null)
                        _buildInfoRow(
                          Icons.payment,
                          'Payment ID',
                          '${order.paymentIntentId!.substring(0, 12)}...',
                        ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Order Items Card
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Order Items',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: theme.primaryColor,
                        ),
                      ),
                      const SizedBox(height: 12),
                      ...order.items.map(
                        (item) => _buildOrderItem(item, theme),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Customer Information Card (if available)
              if (order.customerInfo != null) ...[
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Customer Information',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.primaryColor,
                          ),
                        ),
                        const SizedBox(height: 12),
                        if (order.customerInfo!['name']?.isNotEmpty == true)
                          _buildInfoRow(
                            Icons.person,
                            'Name',
                            order.customerInfo!['name'],
                          ),
                        if (order.customerInfo!['email']?.isNotEmpty == true)
                          _buildInfoRow(
                            Icons.email,
                            'Email',
                            order.customerInfo!['email'],
                          ),
                        if (order.customerInfo!['phone']?.isNotEmpty == true)
                          _buildInfoRow(
                            Icons.phone,
                            'Phone',
                            order.customerInfo!['phone'],
                          ),
                        if (order.customerInfo!['address']?.isNotEmpty == true)
                          _buildInfoRow(
                            Icons.location_on,
                            'Address',
                            order.customerInfo!['address'],
                          ),
                        if (order.customerInfo!['notes']?.isNotEmpty == true)
                          _buildInfoRow(
                            Icons.note,
                            'Notes',
                            order.customerInfo!['notes'],
                          ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Status History Card (if available)
              if (order.statusHistory != null &&
                  order.statusHistory!.isNotEmpty) ...[
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Order Status History',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.primaryColor,
                          ),
                        ),
                        const SizedBox(height: 12),
                        ...order.statusHistory!.map(
                          (statusEntry) =>
                              _buildStatusHistoryItem(statusEntry, theme),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Order Summary Card
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Order Summary',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: theme.primaryColor,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildSummaryRow('Subtotal', order.subtotal),
                      if (order.deliveryFee > 0)
                        _buildSummaryRow('Delivery Fee', order.deliveryFee),
                      _buildSummaryRow('Tax', order.tax),
                      const Divider(thickness: 1),
                      _buildSummaryRow(
                        'Total',
                        order.total,
                        isTotal: true,
                        theme: theme,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Cancel Order Button (if applicable)
              if (order.canBeCancelled)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => _showCancelDialog(order),
                    icon: const Icon(Icons.cancel_outlined),
                    label: const Text('Cancel Order'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red[50],
                      foregroundColor: Colors.red,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
      // Admin floating action button
      floatingActionButton:
          isAdmin && canManageOrders
              ? FloatingActionButton.extended(
                onPressed:
                    _isUpdating ? null : () => _showStatusUpdateDialog(order),
                icon:
                    _isUpdating
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                        : const Icon(Icons.edit),
                label: Text(_isUpdating ? 'Updating...' : 'Update Status'),
                backgroundColor: theme.primaryColor,
              )
              : null,
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: TextStyle(color: Colors.grey[600], fontSize: 14),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItem(Map<String, dynamic> item, ThemeData theme) {
    final name = item['name'] as String;
    final quantity = item['quantity'] as int;
    final price = item['price'] as double;
    final totalPrice = item['totalPrice'] as double;
    final addOns = item['addOns'] as List<dynamic>? ?? [];

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  name,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ),
              Text(
                'TZS ${totalPrice.toStringAsFixed(2)}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: theme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'Qty: $quantity × TZS ${price.toStringAsFixed(2)}',
            style: TextStyle(color: Colors.grey[600], fontSize: 14),
          ),
          if (addOns.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              'Add-ons:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 4),
            ...addOns.map((addOn) {
              final addOnName = addOn['name'] as String;
              final addOnQuantity = addOn['quantity'] as int;
              final addOnPrice = addOn['price'] as double;
              final addOnTotal = addOn['totalPrice'] as double;

              return Padding(
                padding: const EdgeInsets.only(left: 16, bottom: 2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '+ $addOnName (×$addOnQuantity)',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    Text(
                      'TZS ${addOnTotal.toStringAsFixed(2)}',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                  ],
                ),
              );
            }),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    String label,
    double amount, {
    bool isTotal = false,
    ThemeData? theme,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? theme?.primaryColor : Colors.grey[700],
            ),
          ),
          Text(
            'TZS ${amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? theme?.primaryColor : Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusHistoryItem(
    Map<String, dynamic> statusEntry,
    ThemeData theme,
  ) {
    final status = statusEntry['status'] as String;
    final timestamp = statusEntry['timestamp'] as String;
    final note = statusEntry['note'] as String?;

    final parsedTime = DateTime.parse(timestamp);
    final formattedTime = DateFormat('MMM dd, yyyy • HH:mm').format(parsedTime);

    // Parse status to get color
    final orderStatus = OrderStatus.values.firstWhere(
      (s) => s.name == status,
      orElse: () => OrderStatus.pending,
    );
    final statusColor = Color(
      int.parse(_getStatusColor(orderStatus).substring(1, 7), radix: 16) +
          0xFF000000,
    );

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 12,
            height: 12,
            margin: const EdgeInsets.only(top: 4, right: 12),
            decoration: BoxDecoration(
              color: statusColor,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      status.toUpperCase(),
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                        color: statusColor,
                      ),
                    ),
                    Text(
                      formattedTime,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
                if (note != null && note.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    note,
                    style: TextStyle(fontSize: 13, color: Colors.grey[700]),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return '#FF9800';
      case OrderStatus.confirmed:
        return '#2196F3';
      case OrderStatus.preparing:
        return '#FF5722';
      case OrderStatus.ready:
        return '#4CAF50';
      case OrderStatus.outForDelivery:
        return '#9C27B0';
      case OrderStatus.delivered:
        return '#4CAF50';
      case OrderStatus.cancelled:
        return '#F44336';
      case OrderStatus.completed:
        return '#4CAF50';
    }
  }

  void _showCancelDialog(OrderModel order) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Cancel Order'),
            content: const Text('Are you sure you want to cancel this order?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('No, Keep Order'),
              ),
              ElevatedButton(
                onPressed: () async {
                  try {
                    await ref
                        .read(orderServiceProvider)
                        .cancelOrder(order.id, reason: 'Cancelled by customer');
                    if (mounted) {
                      Navigator.pop(context); // Close dialog
                      Navigator.pop(context); // Go back to orders list
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Order cancelled successfully'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (mounted) {
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Failed to cancel order: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Yes, Cancel Order'),
              ),
            ],
          ),
    );
  }

  // Admin action handlers
  void _handleAdminAction(String action, OrderModel order) {
    switch (action) {
      case 'update_status':
        _showStatusUpdateDialog(order);
        break;
      case 'assign_to_me':
        _assignToMe(order);
        break;
      case 'cancel_order':
        _showCancelDialog(order);
        break;
      case 'view_analytics':
        _showOrderAnalytics(order);
        break;
    }
  }

  void _showStatusUpdateDialog(OrderModel order) {
    showDialog(
      context: context,
      builder:
          (context) => OrderStatusUpdateDialog(
            order: order,
            onUpdate: _updateOrderStatus,
          ),
    );
  }

  Future<void> _updateOrderStatus(OrderStatus newStatus, String? note) async {
    final user = ref.read(authServiceProvider).user;
    if (user == null) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      await ref
          .read(adminOrderServiceProvider)
          .updateOrderStatus(
            orderId: widget.orderId,
            newStatus: newStatus,
            staffId: user.uid,
            note: note,
          );

      // Reload order details
      await _loadOrderDetails();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Order status updated to ${newStatus.displayName}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update order: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  Future<void> _assignToMe(OrderModel order) async {
    final user = ref.read(authServiceProvider).user;
    if (user == null) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      await ref
          .read(adminOrderServiceProvider)
          .assignOrderToStaff(
            orderId: order.id,
            staffId: user.uid,
            note: 'Self-assigned from order details',
          );

      // Reload order details
      await _loadOrderDetails();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Order assigned to you'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to assign order: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  void _showOrderAnalytics(OrderModel order) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Order Analytics #${order.id.substring(0, 8)}'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildAnalyticsRow(
                    'Order Value',
                    'TZS ${order.total.toStringAsFixed(0)}',
                  ),
                  _buildAnalyticsRow('Items Count', '${order.items.length}'),
                  _buildAnalyticsRow(
                    'Order Type',
                    order.orderType.name.toUpperCase(),
                  ),
                  _buildAnalyticsRow('Status', order.status.displayName),
                  _buildAnalyticsRow(
                    'Processing Time',
                    _getProcessingTime(order.createdAt, order.updatedAt),
                  ),
                  if (order.assignedStaff != null)
                    _buildAnalyticsRow('Assigned Staff', order.assignedStaff!),
                  const SizedBox(height: 16),
                  const Text(
                    'Revenue Breakdown:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  _buildAnalyticsRow(
                    'Subtotal',
                    'TZS ${order.subtotal.toStringAsFixed(0)}',
                  ),
                  if (order.deliveryFee > 0)
                    _buildAnalyticsRow(
                      'Delivery Fee',
                      'TZS ${order.deliveryFee.toStringAsFixed(0)}',
                    ),
                  _buildAnalyticsRow(
                    'Tax',
                    'TZS ${order.tax.toStringAsFixed(0)}',
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  Widget _buildAnalyticsRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: TextStyle(color: Colors.grey[600])),
          Text(value, style: const TextStyle(fontWeight: FontWeight.w500)),
        ],
      ),
    );
  }

  String _getProcessingTime(DateTime created, DateTime? updated) {
    final endTime = updated ?? DateTime.now();
    final duration = endTime.difference(created);

    if (duration.inDays > 0) {
      return '${duration.inDays}d ${duration.inHours % 24}h';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else {
      return '${duration.inMinutes}m';
    }
  }
}
