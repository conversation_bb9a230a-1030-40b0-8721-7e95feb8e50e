import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;

class PaymentScreen extends ConsumerStatefulWidget {
  const PaymentScreen({super.key});

  @override
  ConsumerState createState() => _PaymentScreenState();
}

class _PaymentScreenState extends ConsumerState<PaymentScreen> {
  bool _isProcessing = false;
  String? _error;

  Future<void> _processPayment() async {
    setState(() {
      _isProcessing = true;
      _error = null;
    });
    try {
      // Mock payment API call
      final response = await http.post(
        Uri.parse('https://mock-payment-api.com/process'),
        body: {'amount': '100.0', 'currency': 'TZS'},
      );
      if (response.statusCode == 200) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Payment Successful!')));
        Navigator.pushReplacementNamed(context, '/home');
      } else {
        throw Exception('Payment failed');
      }
    } catch (e) {
      setState(() {
        _error = 'Payment failed: $e';
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Payment')),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text('Total: TZS 100.00', style: TextStyle(fontSize: 24)),
            if (_error != null)
              Text(_error!, style: TextStyle(color: Colors.red)),
            _isProcessing
                ? CircularProgressIndicator()
                : ElevatedButton(
                  onPressed: _processPayment,
                  child: Text('Pay Now'),
                ),
          ],
        ),
      ),
    );
  }
}
