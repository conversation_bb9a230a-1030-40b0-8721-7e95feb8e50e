import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:smart_restaurant_menu/providers/cart_provider.dart';
import 'package:smart_restaurant_menu/routes/routes.dart';
import 'package:smart_restaurant_menu/services/auth_service.dart';
import '../models/menu_item.dart';
import '../services/database_service.dart';

class ItemDetailScreen extends ConsumerStatefulWidget {
  const ItemDetailScreen({super.key, required this.item});
  final MenuItem item;

  @override
  ConsumerState createState() => _ItemDetailScreenState();
}

class _ItemDetailScreenState extends ConsumerState<ItemDetailScreen> {
  List<Map<String, dynamic>> selectedAddOns = [];
  List<MenuItem> suggestedAddOns = [];
  bool _isLoadingAddOns = true;

  @override
  void initState() {
    super.initState();
    _loadSuggestedAddOns();
  }

  Future<void> _loadSuggestedAddOns() async {
    final item = widget.item;

    final addOns = await ref
        .read(databaseServiceProvider)
        .getSuggestedAddOns(item.savedWith, item.id);

    setState(() {
      suggestedAddOns = addOns;
      _isLoadingAddOns = false;
    });
  }

  void _toggleAddOn(MenuItem addOn) {
    setState(() {
      final existingAddOnIndex = selectedAddOns.indexWhere(
        (addOnEntry) => addOnEntry['item'].id == addOn.id,
      );
      if (existingAddOnIndex != -1) {
        selectedAddOns.removeAt(existingAddOnIndex);
      } else {
        selectedAddOns.add({'item': addOn, 'quantity': 1});
      }
    });
  }

  double _calculateTotal(MenuItem item) {
    double addOnsTotal = selectedAddOns.fold(
      0.0,
      (sum, addOn) => sum + addOn['item'].price * addOn['quantity'],
    );
    return item.price + addOnsTotal;
  }

  void _addToCart(MenuItem item) {
    final cartItem = {
      'item': item,
      'quantity': 1,
      'addOns': selectedAddOns,
      'totalPrice': _calculateTotal(item),
    };

    ref.read(cartProvider.notifier).addToCart(cartItem);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${item.name} added to cart'),
        duration: Duration(seconds: 2),
        action: SnackBarAction(
          label: 'VIEW CART',
          onPressed: () => context.goNamed(AppRoute.cart.name),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final item = widget.item;
    final user = ref.watch(authServiceProvider).user;
    final isFavorite = user?.favorites.contains(item.id) ?? false;
    final totalPrice = _calculateTotal(item);

    return Scaffold(
      body: Stack(
        children: [
          // Main content
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Image
                Container(
                  height: 350,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: NetworkImage(item.imageUrl),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                // Item Details
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '${item.name}, ${item.weight}',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Ingredients: ${item.ingredients.join(', ')}.',
                        style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                      ),
                      SizedBox(height: 16),
                      Text(
                        'Add to order',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8),
                      // Suggested Add-Ons
                      _isLoadingAddOns
                          ? Center(child: CircularProgressIndicator())
                          : SizedBox(
                            height: 170,
                            child: ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              scrollDirection: Axis.horizontal,
                              itemCount: suggestedAddOns.length,
                              itemBuilder: (context, index) {
                                final addOn = suggestedAddOns[index];
                                // selectedAddOns[index]['item'] as MenuItem;
                                final isSelected = selectedAddOns.any(
                                  (entry) => entry['item'].id == addOn.id,
                                );
                                return Padding(
                                  padding: const EdgeInsets.only(right: 16.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Stack(
                                        clipBehavior: Clip.none,
                                        children: [
                                          Container(
                                            width: 110,
                                            height: 110,
                                            decoration: BoxDecoration(
                                              image: DecorationImage(
                                                image: NetworkImage(
                                                  addOn.imageUrl,
                                                ),
                                                fit: BoxFit.cover,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                          ),
                                          Positioned(
                                            top: 4,
                                            right: 4,
                                            child: Center(
                                              child: Container(
                                                height: 25,
                                                width: 25,
                                                decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                        17.5,
                                                      ),
                                                ),
                                                child: IconButton(
                                                  padding: EdgeInsets.zero,
                                                  icon: Icon(
                                                    isSelected
                                                        ? Icons.check_circle
                                                        : Icons.add_circle,
                                                    color:
                                                        Theme.of(
                                                          context,
                                                        ).primaryColor,
                                                  ),
                                                  onPressed:
                                                      () => _toggleAddOn(addOn),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),

                                      Text(
                                        addOn.name,
                                        style: TextStyle(fontSize: 16),
                                      ),
                                      Text(
                                        'TZS ${addOn.price.toStringAsFixed(2)}',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Header
          Positioned(
            top: 310,
            right: 16,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${item.calories} kcal',
                style: TextStyle(fontSize: 14),
              ),
            ),
          ),
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8.0,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: IconButton(
                      icon: Icon(Icons.arrow_back, color: Colors.black),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: IconButton(
                      icon: Icon(
                        isFavorite ? Icons.favorite : Icons.favorite_border,
                        color: Theme.of(context).primaryColor,
                      ),
                      onPressed: () async {
                        if (user != null) {
                          await ref
                              .read(authServiceProvider.notifier)
                              .toggleFavorite(item.id);
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16),
          child: ElevatedButton(
            onPressed: () => _addToCart(item),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              padding: EdgeInsets.symmetric(vertical: 16, horizontal: 32),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'TZS ${totalPrice.toStringAsFixed(2)}',
                  style: TextStyle(fontSize: 18, color: Colors.white),
                ),
                SizedBox(width: 12),
                Container(width: 1, height: 20, color: Colors.white38),
                SizedBox(width: 12),
                Text(
                  'Add to cart',
                  style: TextStyle(fontSize: 18, color: Colors.white),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
