// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyD2K5aht74lhJFNR-N9R3e3WZuJpNLO490',
    appId: '1:800951264304:web:cc186e68e0be02267739f5',
    messagingSenderId: '800951264304',
    projectId: 'smart-restaurant-menu-dit',
    authDomain: 'smart-restaurant-menu-dit.firebaseapp.com',
    storageBucket: 'smart-restaurant-menu-dit.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDTsbKuqwQZFhkANojQ6CjkMpl63ilpx7k',
    appId: '1:800951264304:android:a097619514529b337739f5',
    messagingSenderId: '800951264304',
    projectId: 'smart-restaurant-menu-dit',
    storageBucket: 'smart-restaurant-menu-dit.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyD5OJvuCg-EFIYxQN9-MSgqhYDeE0_FRIc',
    appId: '1:800951264304:ios:ec622f1f43d40d3b7739f5',
    messagingSenderId: '800951264304',
    projectId: 'smart-restaurant-menu-dit',
    storageBucket: 'smart-restaurant-menu-dit.firebasestorage.app',
    iosBundleId: 'com.example.smartRestaurantMenu',
  );
}
