import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/menu_item.dart';
import '../services/secure_storage_service.dart';

class CartNotifier extends StateNotifier<List<Map<String, dynamic>>> {
  final SecureStorageService _secureStorage;

  CartNotifier(this._secureStorage) : super([]) {
    _loadCartFromStorage();
  }

  // Load cart data from secure storage
  Future<void> _loadCartFromStorage() async {
    try {
      final cartData = await _secureStorage.getCartData();
      if (cartData != null && cartData['items'] != null) {
        final items = cartData['items'] as List<dynamic>;
        final cartItems =
            items.map((item) => Map<String, dynamic>.from(item)).toList();
        state = cartItems;
      }
    } catch (e) {
      // If loading fails, start with empty cart
      state = [];
    }
  }

  // Save cart data to secure storage
  Future<void> _saveCartToStorage() async {
    try {
      final cartData = {
        'items': state,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
      await _secureStorage.saveCartData(cartData);
    } catch (e) {
      // Handle storage error silently
    }
  }

  void addToCart(Map<String, dynamic> cartItem) {
    final item = cartItem['item'] as MenuItem;
    final existingIndex = state.indexWhere(
      (element) => (element['item'] as MenuItem).id == item.id,
    );

    if (existingIndex >= 0) {
      // Item already exists, update quantity
      final existingItem = state[existingIndex];
      final updatedItem = {
        ...existingItem,
        'quantity': existingItem['quantity'] + 1,
        'totalPrice': existingItem['totalPrice'] + cartItem['totalPrice'],
      };

      final updatedCart = [...state];
      updatedCart[existingIndex] = updatedItem;
      state = updatedCart;
    } else {
      // Add new item
      state = [...state, cartItem];
    }
    _saveCartToStorage();
  }

  void removeFromCart(int index) {
    final updatedCart = [...state];
    updatedCart.removeAt(index);
    state = updatedCart;
    _saveCartToStorage();
  }

  void updateQuantity(int index, int quantity) {
    if (quantity <= 0) {
      removeFromCart(index);
      return;
    }

    final item = state[index];
    final menuItem = item['item'] as MenuItem;
    final addOnsTotal = (item['addOns'] as List<Map<String, dynamic>>).fold(
      0.0,
      (sum, addOn) =>
          sum + (addOn['item'] as MenuItem).price * addOn['quantity'],
    );

    final updatedItem = {
      ...item,
      'quantity': quantity,
      'totalPrice': (menuItem.price + addOnsTotal) * quantity,
    };

    final updatedCart = [...state];
    updatedCart[index] = updatedItem;
    state = updatedCart;
    _saveCartToStorage();
  }

  void clearCart() {
    state = [];
    _secureStorage.clearCartData();
  }

  double get totalAmount {
    return state.fold(0.0, (sum, item) => sum + (item['totalPrice'] as double));
  }
}

final cartProvider =
    StateNotifierProvider<CartNotifier, List<Map<String, dynamic>>>(
      (ref) => CartNotifier(ref.read(secureStorageServiceProvider)),
    );
