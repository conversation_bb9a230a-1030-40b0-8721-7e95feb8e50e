import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/offline_service.dart';
import '../services/sync_service.dart';

class OfflineIndicator extends ConsumerWidget {
  const OfflineIndicator({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectivityAsync = ref.watch(connectivityProvider);
    final syncStatusAsync = ref.watch(syncStatusProvider);

    return connectivityAsync.when(
      data: (isOnline) {
        if (isOnline) {
          // Show sync status when online
          return syncStatusAsync.when(
            data: (syncStatus) {
              final pendingCount = syncStatus['totalPending'] as int;
              final isSyncing = syncStatus['isSyncing'] as bool;
              
              if (pendingCount > 0 || isSyncing) {
                return _buildSyncIndicator(
                  context,
                  isSyncing: isSyncing,
                  pendingCount: pendingCount,
                );
              }
              return const SizedBox.shrink();
            },
            loading: () => const SizedBox.shrink(),
            error: (_, __) => const SizedBox.shrink(),
          );
        } else {
          // Show offline indicator
          return _buildOfflineIndicator(context);
        }
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => _buildOfflineIndicator(context),
    );
  }

  Widget _buildOfflineIndicator(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.red[600],
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Row(
          children: [
            Icon(
              Icons.cloud_off,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'You\'re offline. Some features may be limited.',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Icon(
              Icons.info_outline,
              color: Colors.white.withOpacity(0.8),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncIndicator(
    BuildContext context, {
    required bool isSyncing,
    required int pendingCount,
  }) {
    final color = isSyncing ? Colors.blue[600] : Colors.orange[600];
    final icon = isSyncing ? Icons.sync : Icons.sync_problem;
    final text = isSyncing 
        ? 'Syncing data...' 
        : '$pendingCount item${pendingCount == 1 ? '' : 's'} pending sync';

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: color,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Row(
          children: [
            if (isSyncing)
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            else
              Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                text,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if (!isSyncing && pendingCount > 0)
              GestureDetector(
                onTap: () => _showSyncOptions(context),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Retry',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showSyncOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => SyncOptionsBottomSheet(),
    );
  }
}

class SyncOptionsBottomSheet extends ConsumerWidget {
  const SyncOptionsBottomSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final syncStatusAsync = ref.watch(syncStatusProvider);

    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.sync,
                color: Theme.of(context).primaryColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Sync Status',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          syncStatusAsync.when(
            data: (syncStatus) {
              final pendingByType = syncStatus['pendingByType'] as Map<String, dynamic>;
              final isOnline = syncStatus['isOnline'] as bool;
              final isSyncing = syncStatus['isSyncing'] as bool;
              
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Connection status
                  _buildStatusRow(
                    icon: isOnline ? Icons.wifi : Icons.wifi_off,
                    label: 'Connection',
                    value: isOnline ? 'Online' : 'Offline',
                    color: isOnline ? Colors.green : Colors.red,
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Sync status
                  _buildStatusRow(
                    icon: isSyncing ? Icons.sync : Icons.check_circle,
                    label: 'Sync Status',
                    value: isSyncing ? 'Syncing...' : 'Ready',
                    color: isSyncing ? Colors.blue : Colors.green,
                  ),
                  
                  if (pendingByType.isNotEmpty) ...[
                    const SizedBox(height: 20),
                    Text(
                      'Pending Items',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...pendingByType.entries.map((entry) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              _formatDataType(entry.key),
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.orange[100],
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                '${entry.value}',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.orange[700],
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }),
                  ],
                  
                  const SizedBox(height: 24),
                  
                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () async {
                            Navigator.of(context).pop();
                            await ref.read(syncServiceProvider).retryFailedSyncs();
                            ref.invalidate(syncStatusProvider);
                          },
                          icon: Icon(Icons.refresh),
                          label: Text('Retry Failed'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: isOnline && !isSyncing ? () async {
                            Navigator.of(context).pop();
                            await ref.read(syncServiceProvider).forceSyncNow();
                            ref.invalidate(syncStatusProvider);
                          } : null,
                          icon: Icon(Icons.sync),
                          label: Text('Sync Now'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).primaryColor,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              );
            },
            loading: () => Center(child: CircularProgressIndicator()),
            error: (error, _) => Text('Error loading sync status'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusRow({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 12),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        const Spacer(),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }

  String _formatDataType(String type) {
    switch (type) {
      case 'orders':
        return 'Orders';
      case 'userProfile':
        return 'Profile';
      case 'favorites':
        return 'Favorites';
      case 'cartData':
        return 'Cart';
      default:
        return type;
    }
  }
}
