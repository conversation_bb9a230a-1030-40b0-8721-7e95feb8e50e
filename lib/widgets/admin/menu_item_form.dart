import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import '../../models/menu_item.dart';
import '../../services/admin_menu_service.dart';
import '../../services/image_upload_service.dart';

import '../../utils/validators.dart';
import '../../utils/currency_formatter.dart';

class MenuItemForm extends ConsumerStatefulWidget {
  final MenuItem? item; // null for create, non-null for edit
  final VoidCallback? onSaved;
  final VoidCallback? onCancel;

  const MenuItemForm({super.key, this.item, this.onSaved, this.onCancel});

  @override
  ConsumerState<MenuItemForm> createState() => _MenuItemFormState();
}

class _MenuItemFormState extends ConsumerState<MenuItemForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _imageUrlController = TextEditingController();
  final _weightController = TextEditingController();
  final _caloriesController = TextEditingController();
  final _customTagController = TextEditingController();

  String _selectedCategory = '';
  bool _isAvailable = true;
  bool _isVegetarian = false;
  bool _isSpicy = false;
  bool _isPopular = false;
  List<String> _allergens = [];
  List<String> _addOns = [];
  List<String> _ingredients = [];
  List<String> _tags = [];
  bool _isLoading = false;
  File? _selectedImage;
  bool _isUploadingImage = false;
  double _uploadProgress = 0.0;
  final ImagePicker _imagePicker = ImagePicker();

  final List<String> _predefinedCategories = [
    'Appetizers',
    'Main Course',
    'Dessert',
    'Beverage',
    'Salads',
    'Soups',
    'Snacks',
    'Breakfast',
    'Lunch',
    'Dinner',
  ];

  final List<String> _commonTags = [
    'Sweet',
    'Healthy',
    'Spicy',
    'Vegetarian',
    'Vegan',
    'Gluten-Free',
    'Low-Carb',
    'High-Protein',
    'Organic',
    'Fresh',
  ];

  final List<String> _commonAllergens = [
    'Nuts',
    'Dairy',
    'Gluten',
    'Eggs',
    'Soy',
    'Shellfish',
    'Fish',
    'Sesame',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.item != null) {
      _populateForm(widget.item!);
    } else {
      _selectedCategory = _predefinedCategories.first;
    }
  }

  void _populateForm(MenuItem item) {
    _nameController.text = item.name;
    _descriptionController.text = item.description;
    _priceController.text = item.price.toString();
    _imageUrlController.text = item.imageUrl;
    _weightController.text = item.weight;
    _caloriesController.text = item.calories.toString();
    _selectedCategory = item.category;
    _isAvailable = item.isAvailable;
    _isVegetarian = item.isVegetarian;
    _isSpicy = item.isSpicy;
    _isPopular = item.isPopular;
    _allergens = List.from(item.allergens);
    _addOns = List.from(item.addOns);
    _ingredients = List.from(item.ingredients);
    _tags = List.from(item.tags);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _imageUrlController.dispose();
    _weightController.dispose();
    _caloriesController.dispose();
    _customTagController.dispose();
    super.dispose();
  }

  void _addCustomTag() {
    final tag = _customTagController.text.trim();
    if (tag.isNotEmpty && !_tags.contains(tag)) {
      setState(() {
        _tags.add(tag);
        _customTagController.clear();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.item == null ? 'Add Menu Item' : 'Edit Menu Item'),
        backgroundColor: theme.primaryColor,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveMenuItem,
              child: const Text(
                'SAVE',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Basic Information Section
              _buildSectionHeader('Basic Information'),
              const SizedBox(height: 12),

              // Name Field
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Item Name *',
                  hintText: 'Enter menu item name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter item name';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Description Field
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description *',
                  hintText: 'Describe the menu item',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter description';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Price and Category Row
              Row(
                children: [
                  // Price Field
                  Expanded(
                    child: TextFormField(
                      controller: _priceController,
                      decoration: InputDecoration(
                        labelText: 'Price (${CurrencyFormatter.currencyCode}) *',
                        hintText: '0.00',
                        border: OutlineInputBorder(),
                        prefixText: '${CurrencyFormatter.currencySymbol} ',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d+\.?\d{0,2}'),
                        ),
                      ],
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter price';
                        }
                        final price = double.tryParse(value);
                        if (price == null || price <= 0) {
                          return 'Please enter valid price';
                        }
                        return null;
                      },
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Category Dropdown
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value:
                          _selectedCategory.isNotEmpty
                              ? _selectedCategory
                              : null,
                      decoration: const InputDecoration(
                        labelText: 'Category *',
                        border: OutlineInputBorder(),
                      ),
                      items:
                          _predefinedCategories.map((category) {
                            return DropdownMenuItem(
                              value: category,
                              child: Text(category),
                            );
                          }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCategory = value ?? '';
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please select category';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Weight and Calories Row
              Row(
                children: [
                  // Weight Field
                  Expanded(
                    child: TextFormField(
                      controller: _weightController,
                      decoration: const InputDecoration(
                        labelText: 'Weight',
                        hintText: 'e.g., 110g',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Calories Field
                  Expanded(
                    child: TextFormField(
                      controller: _caloriesController,
                      decoration: const InputDecoration(
                        labelText: 'Calories',
                        hintText: 'e.g., 460',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Image Section
              _buildSectionHeader('Image'),
              const SizedBox(height: 12),

              // Image Preview and Picker
              Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child:
                    _selectedImage != null
                        ? Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.file(
                                _selectedImage!,
                                width: double.infinity,
                                height: double.infinity,
                                fit: BoxFit.cover,
                              ),
                            ),
                            Positioned(
                              top: 8,
                              right: 8,
                              child: CircleAvatar(
                                backgroundColor: Colors.red,
                                child: IconButton(
                                  icon: const Icon(
                                    Icons.close,
                                    color: Colors.white,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _selectedImage = null;
                                    });
                                  },
                                ),
                              ),
                            ),
                            // Upload progress overlay
                            if (_isUploadingImage)
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.black54,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      CircularProgressIndicator(
                                        value: _uploadProgress,
                                        backgroundColor: Colors.white30,
                                        valueColor:
                                            const AlwaysStoppedAnimation<Color>(
                                              Colors.white,
                                            ),
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        'Uploading... ${(_uploadProgress * 100).toInt()}%',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                          ],
                        )
                        : _imageUrlController.text.isNotEmpty
                        ? Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.network(
                                _imageUrlController.text,
                                width: double.infinity,
                                height: double.infinity,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return const Center(
                                    child: Icon(
                                      Icons.error,
                                      size: 50,
                                      color: Colors.red,
                                    ),
                                  );
                                },
                              ),
                            ),
                            Positioned(
                              top: 8,
                              right: 8,
                              child: CircleAvatar(
                                backgroundColor: Colors.red,
                                child: IconButton(
                                  icon: const Icon(
                                    Icons.close,
                                    color: Colors.white,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _imageUrlController.clear();
                                    });
                                  },
                                ),
                              ),
                            ),
                          ],
                        )
                        : Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.image,
                              size: 50,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'No image selected',
                              style: TextStyle(color: Colors.grey[600]),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton.icon(
                              onPressed: _showImagePickerDialog,
                              icon: const Icon(Icons.photo_library),
                              label: const Text('Pick Image'),
                            ),
                          ],
                        ),
              ),

              const SizedBox(height: 16),

              // Image URL Field (Alternative)
              TextFormField(
                controller: _imageUrlController,
                decoration: const InputDecoration(
                  labelText: 'Or enter Image URL',
                  hintText: 'https://example.com/image.jpg',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value != null &&
                      value.isNotEmpty &&
                      !Validators.isValidUrl(value)) {
                    return 'Please enter a valid URL';
                  }
                  return null;
                },
                onChanged: (value) {
                  if (value.isNotEmpty) {
                    setState(() {
                      _selectedImage =
                          null; // Clear selected image when URL is entered
                    });
                  }
                },
              ),

              const SizedBox(height: 24),

              // Properties Section
              _buildSectionHeader('Properties'),
              const SizedBox(height: 12),

              // Property Switches
              _buildSwitchTile(
                'Available',
                'Item is available for ordering',
                _isAvailable,
                (value) => setState(() => _isAvailable = value),
              ),

              _buildSwitchTile(
                'Vegetarian',
                'Suitable for vegetarians',
                _isVegetarian,
                (value) => setState(() => _isVegetarian = value),
              ),

              _buildSwitchTile(
                'Spicy',
                'Contains spicy ingredients',
                _isSpicy,
                (value) => setState(() => _isSpicy = value),
              ),

              _buildSwitchTile(
                'Popular',
                'Mark as popular item',
                _isPopular,
                (value) => setState(() => _isPopular = value),
              ),

              const SizedBox(height: 24),

              // Allergens Section
              _buildSectionHeader('Allergens'),
              const SizedBox(height: 12),

              Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    _commonAllergens.map((allergen) {
                      final isSelected = _allergens.contains(allergen);
                      return FilterChip(
                        label: Text(allergen),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            if (selected) {
                              _allergens.add(allergen);
                            } else {
                              _allergens.remove(allergen);
                            }
                          });
                        },
                      );
                    }).toList(),
              ),

              const SizedBox(height: 24),

              // Ingredients Section
              _buildSectionHeader('Ingredients'),
              const SizedBox(height: 12),

              ..._ingredients.asMap().entries.map((entry) {
                final index = entry.key;
                final ingredient = entry.value;
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          initialValue: ingredient,
                          decoration: InputDecoration(
                            labelText: 'Ingredient ${index + 1}',
                            hintText: 'e.g., chicken eggs, flour',
                            border: const OutlineInputBorder(),
                          ),
                          onChanged: (value) {
                            _ingredients[index] = value;
                          },
                        ),
                      ),
                      IconButton(
                        icon: const Icon(
                          Icons.remove_circle,
                          color: Colors.red,
                        ),
                        onPressed: () {
                          setState(() {
                            _ingredients.removeAt(index);
                          });
                        },
                      ),
                    ],
                  ),
                );
              }),

              // Add Ingredient Button
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    _ingredients.add('');
                  });
                },
                icon: const Icon(Icons.add),
                label: const Text('Add Ingredient'),
              ),

              const SizedBox(height: 24),

              // Tags Section
              _buildSectionHeader('Tags'),
              const SizedBox(height: 12),

              // Common tags
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  ..._commonTags.map((tag) {
                    final isSelected = _tags.contains(tag);
                    return FilterChip(
                      label: Text(tag),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            _tags.add(tag);
                          } else {
                            _tags.remove(tag);
                          }
                        });
                      },
                    );
                  }),
                  // Show custom tags that aren't in common tags
                  ..._tags.where((tag) => !_commonTags.contains(tag)).map((
                    tag,
                  ) {
                    return Chip(
                      label: Text(tag),
                      deleteIcon: const Icon(Icons.close, size: 18),
                      onDeleted: () {
                        setState(() {
                          _tags.remove(tag);
                        });
                      },
                    );
                  }),
                ],
              ),

              const SizedBox(height: 12),

              // Add custom tag
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _customTagController,
                      decoration: const InputDecoration(
                        labelText: 'Add Custom Tag',
                        hintText: 'e.g., gluten-free, local',
                        border: OutlineInputBorder(),
                      ),
                      onFieldSubmitted: (value) => _addCustomTag(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: _addCustomTag,
                    icon: const Icon(Icons.add),
                    tooltip: 'Add Tag',
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Add-ons Section
              _buildSectionHeader('Add-ons'),
              const SizedBox(height: 12),

              ..._addOns.asMap().entries.map((entry) {
                final index = entry.key;
                final addOn = entry.value;
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          initialValue: addOn,
                          decoration: InputDecoration(
                            labelText: 'Add-on ${index + 1}',
                            border: const OutlineInputBorder(),
                          ),
                          onChanged: (value) {
                            _addOns[index] = value;
                          },
                        ),
                      ),
                      IconButton(
                        icon: const Icon(
                          Icons.remove_circle,
                          color: Colors.red,
                        ),
                        onPressed: () {
                          setState(() {
                            _addOns.removeAt(index);
                          });
                        },
                      ),
                    ],
                  ),
                );
              }),

              // Add Add-on Button
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    _addOns.add('');
                  });
                },
                icon: const Icon(Icons.add),
                label: const Text('Add Add-on'),
              ),

              const SizedBox(height: 32),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed:
                          _isLoading
                              ? null
                              : () {
                                if (widget.onCancel != null) {
                                  widget.onCancel!();
                                } else {
                                  Navigator.of(context).pop();
                                }
                              },
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveMenuItem,
                      child: Text(
                        widget.item == null ? 'Create Item' : 'Update Item',
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      contentPadding: EdgeInsets.zero,
    );
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _imageUrlController.clear(); // Clear URL when image is selected
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to pick image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _takePhoto() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _imageUrlController.clear(); // Clear URL when image is selected
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to take photo: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showImagePickerDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Select Image'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('Choose from Gallery'),
                  onTap: () {
                    Navigator.pop(context);
                    _pickImage();
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.camera_alt),
                  title: const Text('Take Photo'),
                  onTap: () {
                    Navigator.pop(context);
                    _takePhoto();
                  },
                ),
                if (_selectedImage != null ||
                    _imageUrlController.text.isNotEmpty)
                  ListTile(
                    leading: const Icon(Icons.delete, color: Colors.red),
                    title: const Text('Remove Image'),
                    onTap: () {
                      Navigator.pop(context);
                      setState(() {
                        _selectedImage = null;
                        _imageUrlController.clear();
                      });
                    },
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
            ],
          ),
    );
  }

  Future<void> _saveMenuItem() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Clean up lists (remove empty ones)
      final cleanAddOns =
          _addOns.where((addOn) => addOn.trim().isNotEmpty).toList();
      final cleanIngredients =
          _ingredients
              .where((ingredient) => ingredient.trim().isNotEmpty)
              .toList();

      final now = DateTime.now();

      // Handle image upload
      String finalImageUrl = _imageUrlController.text.trim();

      // If user selected a file, upload it to Supabase Storage
      if (_selectedImage != null) {
        try {
          setState(() {
            _isUploadingImage = true;
            _uploadProgress = 0.0;
          });

          // Validate image file
          final imageUploadService = ref.read(imageUploadServiceProvider);
          final validationResult = imageUploadService.validateImage(
            _selectedImage!,
          );

          if (!validationResult.isValid) {
            throw Exception(validationResult.error);
          }

          // Upload image with progress tracking
          final uploadedUrl = await imageUploadService.uploadMenuItemImage(
            imageFile: _selectedImage!,
            menuItemId: widget.item?.id,
            onProgress: (progress) {
              if (mounted) {
                setState(() {
                  _uploadProgress = progress;
                });
              }
            },
          );

          if (uploadedUrl == null) {
            throw Exception('Failed to upload image');
          }

          finalImageUrl = uploadedUrl;

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Image uploaded successfully!'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to upload image: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
          // Use existing image URL or empty string if upload fails
          finalImageUrl = widget.item?.imageUrl ?? '';
        } finally {
          if (mounted) {
            setState(() {
              _isUploadingImage = false;
              _uploadProgress = 0.0;
            });
          }
        }
      }

      final menuItem = MenuItem(
        id: widget.item?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        price: double.parse(_priceController.text),
        category: _selectedCategory,
        imageUrl: finalImageUrl,
        weight: _weightController.text.trim(),
        calories: int.tryParse(_caloriesController.text) ?? 0,
        isAvailable: _isAvailable,
        isVegetarian: _isVegetarian,
        isSpicy: _isSpicy,
        isPopular: _isPopular,
        allergens: _allergens,
        addOns: cleanAddOns,
        ingredients: cleanIngredients,
        tags: _tags, // Add the tags field
        createdAt:
            widget.item?.createdAt ??
            now, // Keep original createdAt for updates
        updatedAt: now, // Always update the updatedAt timestamp
      );

      if (widget.item == null) {
        // Create new item
        await ref.read(adminMenuServiceProvider).createMenuItem(menuItem);
      } else {
        // Update existing item
        await ref.read(adminMenuServiceProvider).updateMenuItem(menuItem);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.item == null
                  ? 'Menu item created successfully'
                  : 'Menu item updated successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );

        if (widget.onSaved != null) {
          widget.onSaved!();
        } else {
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save menu item: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
