import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../models/order.dart';
import '../../routes/routes.dart';
import '../../services/admin_order_service.dart';
import '../../services/auth_service.dart';
import 'order_status_chip.dart';
import 'staff_assignment_dialog.dart';

class AdminOrderCard extends ConsumerStatefulWidget {
  final OrderModel order;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const AdminOrderCard({
    super.key,
    required this.order,
    this.isSelected = false,
    this.onTap,
    this.onLongPress,
  });

  @override
  ConsumerState<AdminOrderCard> createState() => _AdminOrderCardState();
}

class _AdminOrderCardState extends ConsumerState<AdminOrderCard> {
  bool _isUpdating = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final order = widget.order;
    final formattedTime = DateFormat('HH:mm').format(order.createdAt);
    final formattedDate = DateFormat('MMM dd').format(order.createdAt);

    // Calculate time since order
    final timeSinceOrder = DateTime.now().difference(order.createdAt);
    final timeAgo = _formatTimeAgo(timeSinceOrder);

    return Card(
      elevation: widget.isSelected ? 4 : 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side:
            widget.isSelected
                ? BorderSide(color: theme.primaryColor, width: 2)
                : BorderSide.none,
      ),
      child: InkWell(
        onTap:
            widget.onTap ??
            () {
              context.goNamed(
                AppRoute.adminOrderDetails.name,
                pathParameters: {'orderId': order.id},
              );
            },
        onLongPress: widget.onLongPress,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Order ID and Time
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Order #${order.id.substring(0, 8)}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '$formattedDate • $formattedTime ($timeAgo)',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Status and Actions
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      OrderStatusChip(status: order.status),
                      const SizedBox(height: 4),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Quick status update button
                          IconButton(
                            icon: const Icon(Icons.edit, size: 18),
                            onPressed:
                                _isUpdating ? null : _showStatusUpdateDialog,
                            tooltip: 'Update Status',
                          ),
                          // Advanced assignment button
                          PopupMenuButton<String>(
                            icon: const Icon(Icons.person_add, size: 18),
                            enabled: !_isUpdating,
                            tooltip: 'Assignment Options',
                            onSelected:
                                (value) => _handleAssignmentAction(value),
                            itemBuilder:
                                (context) => [
                                  const PopupMenuItem(
                                    value: 'assign_to_me',
                                    child: Row(
                                      children: [
                                        Icon(Icons.person, size: 16),
                                        SizedBox(width: 8),
                                        Text('Assign to Me'),
                                      ],
                                    ),
                                  ),
                                  const PopupMenuItem(
                                    value: 'assign_to_staff',
                                    child: Row(
                                      children: [
                                        Icon(Icons.people, size: 16),
                                        SizedBox(width: 8),
                                        Text('Assign to Staff'),
                                      ],
                                    ),
                                  ),
                                  if (order.assignedStaff != null)
                                    const PopupMenuItem(
                                      value: 'unassign',
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.person_remove,
                                            size: 16,
                                            color: Colors.red,
                                          ),
                                          SizedBox(width: 8),
                                          Text(
                                            'Unassign',
                                            style: TextStyle(color: Colors.red),
                                          ),
                                        ],
                                      ),
                                    ),
                                ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Customer Info
              if (order.customerInfo != null) ...[
                Row(
                  children: [
                    Icon(Icons.person, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        order.customerInfo!['name'] ?? 'Unknown Customer',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],

              // Order Items Summary
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${order.items.length} item${order.items.length > 1 ? 's' : ''}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          'TZS ${order.total.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: theme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),

                    // Show first few items
                    ...order.items.take(2).map((item) {
                      final name = item['name'] as String;
                      final quantity = item['quantity'] as int;
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Row(
                          children: [
                            Container(
                              width: 20,
                              height: 20,
                              decoration: BoxDecoration(
                                color: theme.primaryColor.withValues(
                                  alpha: 0.1,
                                ),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Center(
                                child: Text(
                                  '$quantity',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: theme.primaryColor,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                name,
                                style: const TextStyle(fontSize: 13),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      );
                    }),

                    if (order.items.length > 2)
                      Text(
                        '+ ${order.items.length - 2} more item${order.items.length - 2 > 1 ? 's' : ''}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                  ],
                ),
              ),

              const SizedBox(height: 12),

              // Footer with order type and assigned staff
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Order Type
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: theme.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      order.orderType.name.toUpperCase(),
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: theme.primaryColor,
                      ),
                    ),
                  ),

                  // Assigned Staff or Time Priority
                  if (order.assignedStaff != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.blue[200]!),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.person, size: 14, color: Colors.blue[700]),
                          const SizedBox(width: 4),
                          Text(
                            order.assignedStaff!.length > 15
                                ? '${order.assignedStaff!.substring(0, 15)}...'
                                : order.assignedStaff!,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.blue[700],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    )
                  else if (_isUrgent(timeSinceOrder))
                    Row(
                      children: [
                        Icon(Icons.warning, size: 14, color: Colors.red[600]),
                        const SizedBox(width: 4),
                        Text(
                          'URGENT',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.red[600],
                          ),
                        ),
                      ],
                    ),
                ],
              ),

              // Loading indicator
              if (_isUpdating) ...[
                const SizedBox(height: 8),
                const LinearProgressIndicator(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  String _formatTimeAgo(Duration duration) {
    if (duration.inMinutes < 1) {
      return 'Just now';
    } else if (duration.inMinutes < 60) {
      return '${duration.inMinutes}m ago';
    } else if (duration.inHours < 24) {
      return '${duration.inHours}h ago';
    } else {
      return '${duration.inDays}d ago';
    }
  }

  bool _isUrgent(Duration timeSinceOrder) {
    // Consider orders urgent if they're pending for more than 15 minutes
    return widget.order.status == OrderStatus.pending &&
        timeSinceOrder.inMinutes > 15;
  }

  void _showStatusUpdateDialog() {
    showDialog(
      context: context,
      builder:
          (context) => OrderStatusUpdateDialog(
            order: widget.order,
            onUpdate: _updateOrderStatus,
          ),
    );
  }

  Future<void> _updateOrderStatus(OrderStatus newStatus, String? note) async {
    final user = ref.read(authServiceProvider).user;
    if (user == null) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      await ref
          .read(adminOrderServiceProvider)
          .updateOrderStatus(
            orderId: widget.order.id,
            newStatus: newStatus,
            staffId: user.uid,
            note: note,
          );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Order status updated to ${newStatus.displayName}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update order: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  void _handleAssignmentAction(String action) {
    switch (action) {
      case 'assign_to_me':
        _assignToMe();
        break;
      case 'assign_to_staff':
        _showStaffAssignmentDialog();
        break;
      case 'unassign':
        _unassignOrder();
        break;
    }
  }

  void _showStaffAssignmentDialog() {
    showDialog(
      context: context,
      builder:
          (context) => StaffAssignmentDialog(
            order: widget.order,
            onAssign: _assignOrderToStaff,
          ),
    );
  }

  Future<void> _assignOrderToStaff(String staffId, String? note) async {
    setState(() {
      _isUpdating = true;
    });

    try {
      await ref
          .read(adminOrderServiceProvider)
          .assignOrderToStaff(
            orderId: widget.order.id,
            staffId: staffId,
            note: note,
          );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Order assigned successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to assign order: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  Future<void> _assignToMe() async {
    final user = ref.read(authServiceProvider).user;
    if (user == null) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      await ref
          .read(adminOrderServiceProvider)
          .assignOrderToStaff(
            orderId: widget.order.id,
            staffId: user.uid,
            note: 'Self-assigned from admin panel',
          );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Order assigned to you'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to assign order: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  Future<void> _unassignOrder() async {
    setState(() {
      _isUpdating = true;
    });

    try {
      await ref
          .read(adminOrderServiceProvider)
          .unassignOrderFromStaff(
            orderId: widget.order.id,
            note: 'Unassigned from admin panel',
          );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Order unassigned successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to unassign order: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }
}
