import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/restaurant_settings.dart';
import '../../services/settings_service.dart';
import '../../utils/validators.dart';

class BasicInfoForm extends ConsumerStatefulWidget {
  final RestaurantSettings settings;

  const BasicInfoForm({super.key, required this.settings});

  @override
  ConsumerState<BasicInfoForm> createState() => _BasicInfoFormState();
}

class _BasicInfoFormState extends ConsumerState<BasicInfoForm> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late TextEditingController _addressController;
  late TextEditingController _cityController;
  late TextEditingController _countryController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.settings.name);
    _descriptionController = TextEditingController(
      text: widget.settings.description,
    );
    _emailController = TextEditingController(text: widget.settings.email);
    _phoneController = TextEditingController(text: widget.settings.phone);
    _addressController = TextEditingController(text: widget.settings.address);
    _cityController = TextEditingController(text: widget.settings.city);
    _countryController = TextEditingController(text: widget.settings.country);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _countryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Basic Information',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),

              // Restaurant Name
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Restaurant Name',
                  border: OutlineInputBorder(),
                ),
                validator:
                    (value) =>
                        Validators.validateRequired(value, 'Restaurant name'),
              ),

              const SizedBox(height: 16),

              // Description
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                validator:
                    (value) =>
                        Validators.validateRequired(value, 'Description'),
              ),

              const SizedBox(height: 16),

              // Email
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'Email',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: Validators.validateEmail,
              ),

              const SizedBox(height: 16),

              // Phone
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'Phone',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.phone,
                validator:
                    (value) => Validators.validateRequired(value, 'Phone'),
              ),

              const SizedBox(height: 16),

              // Address
              TextFormField(
                controller: _addressController,
                decoration: const InputDecoration(
                  labelText: 'Address',
                  border: OutlineInputBorder(),
                ),
                validator:
                    (value) => Validators.validateRequired(value, 'Address'),
              ),

              const SizedBox(height: 16),

              // City and Country
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _cityController,
                      decoration: const InputDecoration(
                        labelText: 'City',
                        border: OutlineInputBorder(),
                      ),
                      validator:
                          (value) => Validators.validateRequired(value, 'City'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _countryController,
                      decoration: const InputDecoration(
                        labelText: 'Country',
                        border: OutlineInputBorder(),
                      ),
                      validator:
                          (value) =>
                              Validators.validateRequired(value, 'Country'),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Save Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _saveBasicInfo,
                  child:
                      _isLoading
                          ? const CircularProgressIndicator()
                          : const Text('Save Changes'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveBasicInfo() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await ref
          .read(settingsServiceProvider)
          .updateBasicInfo(
            name: _nameController.text.trim(),
            description: _descriptionController.text.trim(),
            email: _emailController.text.trim(),
            phone: _phoneController.text.trim(),
            address: _addressController.text.trim(),
            city: _cityController.text.trim(),
            country: _countryController.text.trim(),
          );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Basic information updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update basic information: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

class OperatingHoursForm extends ConsumerStatefulWidget {
  final RestaurantSettings settings;

  const OperatingHoursForm({super.key, required this.settings});

  @override
  ConsumerState<OperatingHoursForm> createState() => _OperatingHoursFormState();
}

class _OperatingHoursFormState extends ConsumerState<OperatingHoursForm> {
  late Map<String, OperatingHours> _operatingHours;
  bool _isLoading = false;

  final List<String> _daysOfWeek = [
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
    'saturday',
    'sunday',
  ];

  @override
  void initState() {
    super.initState();
    _operatingHours = Map.from(widget.settings.operatingHours);

    // Ensure all days are present
    for (final day in _daysOfWeek) {
      if (!_operatingHours.containsKey(day)) {
        _operatingHours[day] = OperatingHours();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Operating Hours',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Days of week
            ..._daysOfWeek.map((day) => _buildDayRow(day)),

            const SizedBox(height: 24),

            // Save Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _saveOperatingHours,
                child:
                    _isLoading
                        ? const CircularProgressIndicator()
                        : const Text('Save Operating Hours'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDayRow(String day) {
    final hours = _operatingHours[day]!;
    final dayName = day[0].toUpperCase() + day.substring(1);

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          // Day name
          SizedBox(
            width: 100,
            child: Text(
              dayName,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),

          // Open/Closed toggle
          Switch(
            value: hours.isOpen,
            onChanged: (value) {
              setState(() {
                _operatingHours[day] = OperatingHours(
                  isOpen: value,
                  openTime: hours.openTime,
                  closeTime: hours.closeTime,
                  breaks: hours.breaks,
                );
              });
            },
          ),

          const SizedBox(width: 16),

          // Time pickers (only if open)
          if (hours.isOpen) ...[
            // Open time
            Expanded(
              child: InkWell(
                onTap: () => _selectTime(day, true),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(hours.openTime),
                ),
              ),
            ),

            const SizedBox(width: 8),
            const Text('to'),
            const SizedBox(width: 8),

            // Close time
            Expanded(
              child: InkWell(
                onTap: () => _selectTime(day, false),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(hours.closeTime),
                ),
              ),
            ),
          ] else ...[
            const Expanded(
              child: Text(
                'Closed',
                style: TextStyle(
                  color: Colors.grey,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _selectTime(String day, bool isOpenTime) async {
    final currentHours = _operatingHours[day]!;
    final currentTime =
        isOpenTime ? currentHours.openTime : currentHours.closeTime;

    final timeParts = currentTime.split(':');
    final initialTime = TimeOfDay(
      hour: int.parse(timeParts[0]),
      minute: int.parse(timeParts[1]),
    );

    final selectedTime = await showTimePicker(
      context: context,
      initialTime: initialTime,
    );

    if (selectedTime != null) {
      final timeString =
          '${selectedTime.hour.toString().padLeft(2, '0')}:${selectedTime.minute.toString().padLeft(2, '0')}';

      setState(() {
        _operatingHours[day] = OperatingHours(
          isOpen: currentHours.isOpen,
          openTime: isOpenTime ? timeString : currentHours.openTime,
          closeTime: isOpenTime ? currentHours.closeTime : timeString,
          breaks: currentHours.breaks,
        );
      });
    }
  }

  Future<void> _saveOperatingHours() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await ref
          .read(settingsServiceProvider)
          .updateOperatingHours(_operatingHours);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Operating hours updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update operating hours: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

class PaymentSettingsForm extends ConsumerStatefulWidget {
  final RestaurantSettings settings;

  const PaymentSettingsForm({super.key, required this.settings});

  @override
  ConsumerState<PaymentSettingsForm> createState() =>
      _PaymentSettingsFormState();
}

class _PaymentSettingsFormState extends ConsumerState<PaymentSettingsForm> {
  late PaymentSettings _paymentSettings;
  late TextEditingController _taxRateController;
  late TextEditingController _serviceChargeController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _paymentSettings = widget.settings.paymentSettings;
    _taxRateController = TextEditingController(
      text: (_paymentSettings.taxRate * 100).toStringAsFixed(1),
    );
    _serviceChargeController = TextEditingController(
      text: (_paymentSettings.serviceCharge * 100).toStringAsFixed(1),
    );
  }

  @override
  void dispose() {
    _taxRateController.dispose();
    _serviceChargeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Payment Settings',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Payment Methods
            const Text(
              'Accepted Payment Methods',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),

            CheckboxListTile(
              title: const Text('Cash'),
              subtitle: const Text('Accept cash payments'),
              value: _paymentSettings.acceptCash,
              onChanged: (value) {
                setState(() {
                  _paymentSettings = PaymentSettings(
                    acceptCash: value ?? false,
                    acceptCard: _paymentSettings.acceptCard,
                    acceptMobileMoney: _paymentSettings.acceptMobileMoney,
                    taxRate: _paymentSettings.taxRate,
                    serviceCharge: _paymentSettings.serviceCharge,
                    defaultPaymentMethod: _paymentSettings.defaultPaymentMethod,
                    gatewaySettings: _paymentSettings.gatewaySettings,
                  );
                });
              },
            ),

            CheckboxListTile(
              title: const Text('Card'),
              subtitle: const Text('Accept credit/debit cards'),
              value: _paymentSettings.acceptCard,
              onChanged: (value) {
                setState(() {
                  _paymentSettings = PaymentSettings(
                    acceptCash: _paymentSettings.acceptCash,
                    acceptCard: value ?? false,
                    acceptMobileMoney: _paymentSettings.acceptMobileMoney,
                    taxRate: _paymentSettings.taxRate,
                    serviceCharge: _paymentSettings.serviceCharge,
                    defaultPaymentMethod: _paymentSettings.defaultPaymentMethod,
                    gatewaySettings: _paymentSettings.gatewaySettings,
                  );
                });
              },
            ),

            CheckboxListTile(
              title: const Text('Mobile Money'),
              subtitle: const Text('Accept M-Pesa, Tigo Pesa, etc.'),
              value: _paymentSettings.acceptMobileMoney,
              onChanged: (value) {
                setState(() {
                  _paymentSettings = PaymentSettings(
                    acceptCash: _paymentSettings.acceptCash,
                    acceptCard: _paymentSettings.acceptCard,
                    acceptMobileMoney: value ?? false,
                    taxRate: _paymentSettings.taxRate,
                    serviceCharge: _paymentSettings.serviceCharge,
                    defaultPaymentMethod: _paymentSettings.defaultPaymentMethod,
                    gatewaySettings: _paymentSettings.gatewaySettings,
                  );
                });
              },
            ),

            const SizedBox(height: 16),

            // Tax Rate
            TextFormField(
              controller: _taxRateController,
              decoration: const InputDecoration(
                labelText: 'Tax Rate (%)',
                border: OutlineInputBorder(),
                suffixText: '%',
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                final rate = double.tryParse(value) ?? 0.0;
                _paymentSettings = PaymentSettings(
                  acceptCash: _paymentSettings.acceptCash,
                  acceptCard: _paymentSettings.acceptCard,
                  acceptMobileMoney: _paymentSettings.acceptMobileMoney,
                  taxRate: rate / 100,
                  serviceCharge: _paymentSettings.serviceCharge,
                  defaultPaymentMethod: _paymentSettings.defaultPaymentMethod,
                  gatewaySettings: _paymentSettings.gatewaySettings,
                );
              },
            ),

            const SizedBox(height: 16),

            // Service Charge
            TextFormField(
              controller: _serviceChargeController,
              decoration: const InputDecoration(
                labelText: 'Service Charge (%)',
                border: OutlineInputBorder(),
                suffixText: '%',
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                final charge = double.tryParse(value) ?? 0.0;
                _paymentSettings = PaymentSettings(
                  acceptCash: _paymentSettings.acceptCash,
                  acceptCard: _paymentSettings.acceptCard,
                  acceptMobileMoney: _paymentSettings.acceptMobileMoney,
                  taxRate: _paymentSettings.taxRate,
                  serviceCharge: charge / 100,
                  defaultPaymentMethod: _paymentSettings.defaultPaymentMethod,
                  gatewaySettings: _paymentSettings.gatewaySettings,
                );
              },
            ),

            const SizedBox(height: 24),

            // Save Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _savePaymentSettings,
                child:
                    _isLoading
                        ? const CircularProgressIndicator()
                        : const Text('Save Payment Settings'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _savePaymentSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await ref
          .read(settingsServiceProvider)
          .updatePaymentSettings(_paymentSettings);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Payment settings updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update payment settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

class NotificationSettingsForm extends ConsumerStatefulWidget {
  final RestaurantSettings settings;

  const NotificationSettingsForm({super.key, required this.settings});

  @override
  ConsumerState<NotificationSettingsForm> createState() =>
      _NotificationSettingsFormState();
}

class _NotificationSettingsFormState
    extends ConsumerState<NotificationSettingsForm> {
  late NotificationSettings _notificationSettings;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _notificationSettings = widget.settings.notificationSettings;
    _emailController = TextEditingController(
      text: _notificationSettings.notificationEmail,
    );
    _phoneController = TextEditingController(
      text: _notificationSettings.notificationPhone,
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Notification Settings',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Notification Types
            SwitchListTile(
              title: const Text('Email Notifications'),
              subtitle: const Text('Receive notifications via email'),
              value: _notificationSettings.emailNotifications,
              onChanged: (value) {
                setState(() {
                  _notificationSettings = NotificationSettings(
                    emailNotifications: value,
                    smsNotifications: _notificationSettings.smsNotifications,
                    pushNotifications: _notificationSettings.pushNotifications,
                    orderNotifications:
                        _notificationSettings.orderNotifications,
                    paymentNotifications:
                        _notificationSettings.paymentNotifications,
                    staffNotifications:
                        _notificationSettings.staffNotifications,
                    notificationEmail: _notificationSettings.notificationEmail,
                    notificationPhone: _notificationSettings.notificationPhone,
                  );
                });
              },
            ),

            SwitchListTile(
              title: const Text('SMS Notifications'),
              subtitle: const Text('Receive notifications via SMS'),
              value: _notificationSettings.smsNotifications,
              onChanged: (value) {
                setState(() {
                  _notificationSettings = NotificationSettings(
                    emailNotifications:
                        _notificationSettings.emailNotifications,
                    smsNotifications: value,
                    pushNotifications: _notificationSettings.pushNotifications,
                    orderNotifications:
                        _notificationSettings.orderNotifications,
                    paymentNotifications:
                        _notificationSettings.paymentNotifications,
                    staffNotifications:
                        _notificationSettings.staffNotifications,
                    notificationEmail: _notificationSettings.notificationEmail,
                    notificationPhone: _notificationSettings.notificationPhone,
                  );
                });
              },
            ),

            SwitchListTile(
              title: const Text('Push Notifications'),
              subtitle: const Text('Receive push notifications in app'),
              value: _notificationSettings.pushNotifications,
              onChanged: (value) {
                setState(() {
                  _notificationSettings = NotificationSettings(
                    emailNotifications:
                        _notificationSettings.emailNotifications,
                    smsNotifications: _notificationSettings.smsNotifications,
                    pushNotifications: value,
                    orderNotifications:
                        _notificationSettings.orderNotifications,
                    paymentNotifications:
                        _notificationSettings.paymentNotifications,
                    staffNotifications:
                        _notificationSettings.staffNotifications,
                    notificationEmail: _notificationSettings.notificationEmail,
                    notificationPhone: _notificationSettings.notificationPhone,
                  );
                });
              },
            ),

            const Divider(),

            // Notification Categories
            const Text(
              'Notification Categories',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),

            CheckboxListTile(
              title: const Text('Order Notifications'),
              subtitle: const Text('New orders, status changes'),
              value: _notificationSettings.orderNotifications,
              onChanged: (value) {
                setState(() {
                  _notificationSettings = NotificationSettings(
                    emailNotifications:
                        _notificationSettings.emailNotifications,
                    smsNotifications: _notificationSettings.smsNotifications,
                    pushNotifications: _notificationSettings.pushNotifications,
                    orderNotifications: value ?? false,
                    paymentNotifications:
                        _notificationSettings.paymentNotifications,
                    staffNotifications:
                        _notificationSettings.staffNotifications,
                    notificationEmail: _notificationSettings.notificationEmail,
                    notificationPhone: _notificationSettings.notificationPhone,
                  );
                });
              },
            ),

            CheckboxListTile(
              title: const Text('Payment Notifications'),
              subtitle: const Text('Payment confirmations, failures'),
              value: _notificationSettings.paymentNotifications,
              onChanged: (value) {
                setState(() {
                  _notificationSettings = NotificationSettings(
                    emailNotifications:
                        _notificationSettings.emailNotifications,
                    smsNotifications: _notificationSettings.smsNotifications,
                    pushNotifications: _notificationSettings.pushNotifications,
                    orderNotifications:
                        _notificationSettings.orderNotifications,
                    paymentNotifications: value ?? false,
                    staffNotifications:
                        _notificationSettings.staffNotifications,
                    notificationEmail: _notificationSettings.notificationEmail,
                    notificationPhone: _notificationSettings.notificationPhone,
                  );
                });
              },
            ),

            CheckboxListTile(
              title: const Text('Staff Notifications'),
              subtitle: const Text('Staff activities, role changes'),
              value: _notificationSettings.staffNotifications,
              onChanged: (value) {
                setState(() {
                  _notificationSettings = NotificationSettings(
                    emailNotifications:
                        _notificationSettings.emailNotifications,
                    smsNotifications: _notificationSettings.smsNotifications,
                    pushNotifications: _notificationSettings.pushNotifications,
                    orderNotifications:
                        _notificationSettings.orderNotifications,
                    paymentNotifications:
                        _notificationSettings.paymentNotifications,
                    staffNotifications: value ?? false,
                    notificationEmail: _notificationSettings.notificationEmail,
                    notificationPhone: _notificationSettings.notificationPhone,
                  );
                });
              },
            ),

            const SizedBox(height: 16),

            // Contact Information
            if (_notificationSettings.emailNotifications) ...[
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'Notification Email',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.emailAddress,
                onChanged: (value) {
                  _notificationSettings = NotificationSettings(
                    emailNotifications:
                        _notificationSettings.emailNotifications,
                    smsNotifications: _notificationSettings.smsNotifications,
                    pushNotifications: _notificationSettings.pushNotifications,
                    orderNotifications:
                        _notificationSettings.orderNotifications,
                    paymentNotifications:
                        _notificationSettings.paymentNotifications,
                    staffNotifications:
                        _notificationSettings.staffNotifications,
                    notificationEmail: value,
                    notificationPhone: _notificationSettings.notificationPhone,
                  );
                },
              ),
              const SizedBox(height: 16),
            ],

            if (_notificationSettings.smsNotifications) ...[
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'Notification Phone',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.phone,
                onChanged: (value) {
                  _notificationSettings = NotificationSettings(
                    emailNotifications:
                        _notificationSettings.emailNotifications,
                    smsNotifications: _notificationSettings.smsNotifications,
                    pushNotifications: _notificationSettings.pushNotifications,
                    orderNotifications:
                        _notificationSettings.orderNotifications,
                    paymentNotifications:
                        _notificationSettings.paymentNotifications,
                    staffNotifications:
                        _notificationSettings.staffNotifications,
                    notificationEmail: _notificationSettings.notificationEmail,
                    notificationPhone: value,
                  );
                },
              ),
              const SizedBox(height: 16),
            ],

            // Save Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _saveNotificationSettings,
                child:
                    _isLoading
                        ? const CircularProgressIndicator()
                        : const Text('Save Notification Settings'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveNotificationSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await ref
          .read(settingsServiceProvider)
          .updateNotificationSettings(_notificationSettings);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notification settings updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update notification settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
