import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/user.dart';
import '../../models/order.dart';
import '../../services/settings_service.dart';

class StaffAssignmentDialog extends ConsumerStatefulWidget {
  final OrderModel? order;
  final List<String>? orderIds;
  final Function(String staffId, String? note) onAssign;

  const StaffAssignmentDialog({
    super.key,
    this.order,
    this.orderIds,
    required this.onAssign,
  });

  @override
  ConsumerState<StaffAssignmentDialog> createState() => _StaffAssignmentDialogState();
}

class _StaffAssignmentDialogState extends ConsumerState<StaffAssignmentDialog> {
  String? _selectedStaffId;
  final _noteController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  bool get _isBulkAssignment => widget.orderIds != null && widget.orderIds!.isNotEmpty;

  @override
  Widget build(BuildContext context) {
    final staffListAsync = ref.watch(allStaffStreamProvider);

    return AlertDialog(
      title: Text(
        _isBulkAssignment 
          ? 'Assign ${widget.orderIds!.length} Orders to Staff'
          : 'Assign Order #${widget.order?.id.substring(0, 8)} to Staff',
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!_isBulkAssignment && widget.order != null) ...[
              // Current assignment info
              if (widget.order!.assignedStaff != null) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue[700], size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Currently assigned to: ${widget.order!.assignedStaff}',
                          style: TextStyle(color: Colors.blue[700]),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ],

            // Staff selection
            const Text(
              'Select Staff Member:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),

            staffListAsync.when(
              data: (staffList) => _buildStaffSelection(staffList),
              loading: () => const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: CircularProgressIndicator(),
                ),
              ),
              error: (error, stack) => Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Error loading staff: $error',
                  style: TextStyle(color: Colors.red[700]),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Note field
            const Text(
              'Assignment Note (Optional):',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _noteController,
              decoration: const InputDecoration(
                hintText: 'Add a note about this assignment...',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _selectedStaffId != null && !_isLoading
              ? _handleAssignment
              : null,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Assign'),
        ),
      ],
    );
  }

  Widget _buildStaffSelection(List<AppUser> staffList) {
    if (staffList.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Column(
          children: [
            Icon(Icons.people_outline, size: 48, color: Colors.grey),
            SizedBox(height: 8),
            Text(
              'No staff members available',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Container(
      constraints: const BoxConstraints(maxHeight: 300),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: staffList.length,
        itemBuilder: (context, index) {
          final staff = staffList[index];
          final isSelected = _selectedStaffId == staff.uid;

          return Card(
            margin: const EdgeInsets.only(bottom: 8),
            color: isSelected ? Colors.blue[50] : null,
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: _getRoleColor(staff.role),
                child: Text(
                  staff.name.isNotEmpty ? staff.name[0].toUpperCase() : 'U',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              title: Text(
                staff.name,
                style: TextStyle(
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(_getRoleDisplayName(staff.role)),
                  if (!staff.isActive)
                    Text(
                      'Inactive',
                      style: TextStyle(
                        color: Colors.red[600],
                        fontSize: 12,
                      ),
                    ),
                ],
              ),
              trailing: isSelected
                  ? Icon(Icons.check_circle, color: Colors.blue[700])
                  : null,
              enabled: staff.isActive,
              onTap: staff.isActive
                  ? () {
                      setState(() {
                        _selectedStaffId = staff.uid;
                      });
                    }
                  : null,
            ),
          );
        },
      ),
    );
  }

  void _handleAssignment() async {
    if (_selectedStaffId == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final note = _noteController.text.trim().isEmpty 
          ? null 
          : _noteController.text.trim();
      
      widget.onAssign(_selectedStaffId!, note);
      
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to assign: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return Colors.red;
      case UserRole.manager:
        return Colors.purple;
      case UserRole.staff:
        return Colors.blue;
      case UserRole.customer:
        return Colors.grey;
    }
  }

  String _getRoleDisplayName(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return 'Administrator';
      case UserRole.manager:
        return 'Manager';
      case UserRole.staff:
        return 'Staff';
      case UserRole.customer:
        return 'Customer';
    }
  }
}

// Provider for staff list stream
final allStaffStreamProvider = StreamProvider<List<AppUser>>((ref) {
  return ref.read(settingsServiceProvider).getAllStaffStream();
});
