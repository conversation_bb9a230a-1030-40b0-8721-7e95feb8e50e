import 'package:flutter/material.dart';
import '../../models/order.dart';

class OrderStatusChip extends StatelessWidget {
  final OrderStatus status;
  final VoidCallback? onTap;
  final bool isSelected;

  const OrderStatusChip({
    super.key,
    required this.status,
    this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final color = _getStatusColor(status);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? color : color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Text(
          status.displayName,
          style: TextStyle(
            color: isSelected ? Colors.white : color,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.w600,
            fontSize: 12,
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.confirmed:
        return Colors.blue;
      case OrderStatus.preparing:
        return Colors.purple;
      case OrderStatus.ready:
        return Colors.green;
      case OrderStatus.outForDelivery:
        return Colors.indigo;
      case OrderStatus.delivered:
        return Colors.green;
      case OrderStatus.completed:
        return Colors.teal;
      case OrderStatus.cancelled:
        return Colors.red;
    }
  }
}

class OrderStatusSelector extends StatelessWidget {
  final OrderStatus currentStatus;
  final Function(OrderStatus) onStatusChanged;
  final List<OrderStatus>? allowedStatuses;

  const OrderStatusSelector({
    super.key,
    required this.currentStatus,
    required this.onStatusChanged,
    this.allowedStatuses,
  });

  @override
  Widget build(BuildContext context) {
    final statuses = allowedStatuses ?? _getNextAllowedStatuses(currentStatus);

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: statuses.map((status) {
        return OrderStatusChip(
          status: status,
          isSelected: status == currentStatus,
          onTap: () => onStatusChanged(status),
        );
      }).toList(),
    );
  }

  List<OrderStatus> _getNextAllowedStatuses(OrderStatus current) {
    switch (current) {
      case OrderStatus.pending:
        return [OrderStatus.pending, OrderStatus.confirmed, OrderStatus.cancelled];
      case OrderStatus.confirmed:
        return [OrderStatus.confirmed, OrderStatus.preparing, OrderStatus.cancelled];
      case OrderStatus.preparing:
        return [OrderStatus.preparing, OrderStatus.ready, OrderStatus.cancelled];
      case OrderStatus.ready:
        return [OrderStatus.ready, OrderStatus.outForDelivery, OrderStatus.completed];
      case OrderStatus.outForDelivery:
        return [OrderStatus.outForDelivery, OrderStatus.delivered];
      case OrderStatus.delivered:
        return [OrderStatus.delivered, OrderStatus.completed];
      case OrderStatus.completed:
        return [OrderStatus.completed];
      case OrderStatus.cancelled:
        return [OrderStatus.cancelled];
    }
  }
}

class OrderStatusUpdateDialog extends StatefulWidget {
  final OrderModel order;
  final Function(OrderStatus, String?) onUpdate;

  const OrderStatusUpdateDialog({
    super.key,
    required this.order,
    required this.onUpdate,
  });

  @override
  State<OrderStatusUpdateDialog> createState() => _OrderStatusUpdateDialogState();
}

class _OrderStatusUpdateDialogState extends State<OrderStatusUpdateDialog> {
  late OrderStatus _selectedStatus;
  final _noteController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.order.status;
  }

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Update Order #${widget.order.id.substring(0, 8)}'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Status: ${widget.order.status.displayName}',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),
            
            const Text(
              'Select New Status:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            
            OrderStatusSelector(
              currentStatus: _selectedStatus,
              onStatusChanged: (status) {
                setState(() {
                  _selectedStatus = status;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            const Text(
              'Note (Optional):',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            
            TextField(
              controller: _noteController,
              decoration: const InputDecoration(
                hintText: 'Add a note about this status change...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _selectedStatus != widget.order.status
              ? () {
                  widget.onUpdate(
                    _selectedStatus,
                    _noteController.text.trim().isEmpty 
                        ? null 
                        : _noteController.text.trim(),
                  );
                  Navigator.of(context).pop();
                }
              : null,
          child: const Text('Update Status'),
        ),
      ],
    );
  }
}

// Extension to add display names for order status
extension OrderStatusExtension on OrderStatus {
  String get displayName {
    switch (this) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.preparing:
        return 'Preparing';
      case OrderStatus.ready:
        return 'Ready';
      case OrderStatus.outForDelivery:
        return 'Out for Delivery';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.completed:
        return 'Completed';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get description {
    switch (this) {
      case OrderStatus.pending:
        return 'Order received, waiting for confirmation';
      case OrderStatus.confirmed:
        return 'Order confirmed, preparing to cook';
      case OrderStatus.preparing:
        return 'Food is being prepared';
      case OrderStatus.ready:
        return 'Order is ready for pickup/delivery';
      case OrderStatus.outForDelivery:
        return 'Order is out for delivery';
      case OrderStatus.delivered:
        return 'Order has been delivered';
      case OrderStatus.completed:
        return 'Order completed successfully';
      case OrderStatus.cancelled:
        return 'Order has been cancelled';
    }
  }
}
