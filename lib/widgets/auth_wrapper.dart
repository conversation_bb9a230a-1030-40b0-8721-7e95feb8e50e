import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../services/auth_service.dart';

/// A wrapper widget that handles authentication state and redirects users appropriately
class AuthWrapper extends ConsumerStatefulWidget {
  final Widget child;
  final String currentRoute;

  const AuthWrapper({
    super.key,
    required this.child,
    required this.currentRoute,
  });

  @override
  ConsumerState<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends ConsumerState<AuthWrapper> {
  bool _hasCheckedAuth = false;

  @override
  void initState() {
    super.initState();
    // Delay the auth check to ensure the widget is fully built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAuthAndRedirect();
    });
  }

  void _checkAuthAndRedirect() {
    if (_hasCheckedAuth) return;

    final authState = ref.read(authServiceProvider);

    print(
      'AuthWrapper: Auth check - Loading: ${authState.isLoading}, User: ${authState.user?.email}, Error: ${authState.error}',
    );

    // Don't redirect if still loading
    if (authState.isLoading) {
      print('AuthWrapper: Still loading, waiting...');
      return;
    }

    _hasCheckedAuth = true;

    final currentRoute = widget.currentRoute;
    print('AuthWrapper: Checking auth for route: $currentRoute');

    if (authState.user != null) {
      print(
        'AuthWrapper: User found - Email: ${authState.user!.email}, Role: ${authState.user!.role.name}, IsAdminUser: ${authState.user!.isAdminUser}',
      );
    } else {
      print('AuthWrapper: No user found in auth state');
    }

    // Add a small delay to prevent rapid redirects
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _performRedirect(authState, currentRoute);
      }
    });
  }

  void _performRedirect(AuthState authState, String currentRoute) {
    // Public routes that don't require authentication
    const publicRoutes = ['/login', '/register', '/password-reset', '/splash'];
    final isPublicRoute = publicRoutes.contains(currentRoute);

    // If no user is authenticated
    if (authState.user == null) {
      if (!isPublicRoute) {
        print('AuthWrapper: No user, redirecting to login');
        context.go('/login');
      }
      return;
    }

    // User is authenticated - check email verification
    if (!authState.isEmailVerified) {
      if (currentRoute != '/email-verification') {
        print('AuthWrapper: Email not verified, redirecting to verification');
        context.go('/email-verification');
      }
      return;
    }

    // User is authenticated and email verified
    final user = authState.user!;
    final isAdminRoute = currentRoute.startsWith('/admin/');

    // Handle role-based routing
    if (user.isAdminUser) {
      // Admin/Staff/Manager users
      if (currentRoute == '/' ||
          currentRoute == '/login' ||
          currentRoute == '/splash') {
        print(
          'AuthWrapper: Admin user on auth/customer route, redirecting to admin dashboard',
        );
        context.go('/admin/dashboard');
        return;
      }
      if (!isAdminRoute && !_isAllowedCustomerRouteForAdmin(currentRoute)) {
        print(
          'AuthWrapper: Admin user accessing restricted customer route, redirecting to admin dashboard',
        );
        context.go('/admin/dashboard');
        return;
      }
    } else {
      // Customer users
      if (isAdminRoute) {
        print(
          'AuthWrapper: Customer user trying to access admin route, redirecting to home',
        );
        context.go('/');
        return;
      }
      if (currentRoute == '/login' || currentRoute == '/splash') {
        print('AuthWrapper: Customer user on auth route, redirecting to home');
        context.go('/');
        return;
      }
    }

    print('AuthWrapper: User authorized for route: $currentRoute');
  }

  bool _isAllowedCustomerRouteForAdmin(String route) {
    // Admin users can access some customer routes like profile, orders, etc.
    const allowedRoutes = [
      '/profile',
      '/orders',
      '/cart',
      '/checkout',
      '/payment-success',
      '/email-verification',
    ];

    return allowedRoutes.contains(route) ||
        route.startsWith('/orders/') ||
        route.startsWith('/menu_item');
  }

  @override
  Widget build(BuildContext context) {
    // Listen to auth state changes and re-check when needed
    ref.listen<AuthState>(authServiceProvider, (previous, next) {
      print(
        'AuthWrapper: Auth state changed - Previous: ${previous?.user?.email}, Next: ${next.user?.email}',
      );
      print(
        'AuthWrapper: Loading changed - Previous: ${previous?.isLoading}, Next: ${next.isLoading}',
      );

      // Reset check flag when auth state changes significantly
      if ((previous?.user?.uid != next.user?.uid) ||
          (previous?.isLoading == true && next.isLoading == false)) {
        print('AuthWrapper: Significant auth change detected, re-checking...');
        _hasCheckedAuth = false;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _checkAuthAndRedirect();
          }
        });
      }
    });

    final authState = ref.watch(authServiceProvider);

    // Always try to check auth when building (but only once per state)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _checkAuthAndRedirect();
      }
    });

    // Show loading screen while auth is loading
    if (authState.isLoading) {
      return Scaffold(
        backgroundColor: Colors.deepOrange[700],
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.restaurant, size: 80, color: Colors.white),
              SizedBox(height: 24),
              Text(
                'Smart Restaurant Menu',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              SizedBox(height: 32),
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
              SizedBox(height: 16),
              Text(
                'Loading...',
                style: TextStyle(fontSize: 16, color: Colors.white70),
              ),
            ],
          ),
        ),
      );
    }

    // Show the actual content
    return widget.child;
  }
}

/// Extension to easily wrap routes with AuthWrapper
extension GoRouteAuthExtension on GoRoute {
  GoRoute withAuth() {
    return GoRoute(
      path: path,
      name: name,
      builder: (context, state) {
        final originalChild = builder!(context, state);
        return AuthWrapper(currentRoute: state.uri.path, child: originalChild);
      },
      routes: routes,
    );
  }
}
