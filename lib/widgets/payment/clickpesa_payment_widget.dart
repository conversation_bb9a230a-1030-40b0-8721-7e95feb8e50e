// File: lib/widgets/payment/clickpesa_payment_widget.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:smart_restaurant_menu/services/clickpesa_service.dart';
import 'package:smart_restaurant_menu/utils/currency_formatter.dart';

class ClickPesaPaymentWidget extends ConsumerStatefulWidget {
  final double amount;
  final String orderId;
  final String customerName;
  final String customerEmail;
  final Function(ClickPesaPaymentResult) onPaymentResult;

  const ClickPesaPaymentWidget({
    Key? key,
    required this.amount,
    required this.orderId,
    required this.customerName,
    required this.customerEmail,
    required this.onPaymentResult,
  }) : super(key: key);

  @override
  ConsumerState<ClickPesaPaymentWidget> createState() => _ClickPesaPaymentWidgetState();
}

class _ClickPesaPaymentWidgetState extends ConsumerState<ClickPesaPaymentWidget> {
  final _phoneController = TextEditingController();
  ClickPesaPaymentMethod? _selectedPaymentMethod;
  bool _isProcessing = false;
  String? _errorMessage;

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _processPayment() async {
    if (_selectedPaymentMethod == null) {
      setState(() {
        _errorMessage = 'Please select a payment method';
      });
      return;
    }

    final phone = _phoneController.text.trim();
    if (phone.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter your phone number';
      });
      return;
    }

    final clickPesaService = ref.read(clickPesaServiceProvider);
    if (!clickPesaService.isValidMobileMoneyPhone(phone)) {
      setState(() {
        _errorMessage = 'Please enter a valid Tanzanian phone number';
      });
      return;
    }

    setState(() {
      _isProcessing = true;
      _errorMessage = null;
    });

    try {
      final result = await clickPesaService.createPayment(
        amount: widget.amount,
        customerPhone: clickPesaService.formatPhoneNumber(phone),
        customerEmail: widget.customerEmail,
        customerName: widget.customerName,
        paymentMethod: _selectedPaymentMethod!,
        orderId: widget.orderId,
        description: 'Restaurant Order Payment - ${widget.orderId}',
      );

      widget.onPaymentResult(result);
    } catch (e) {
      setState(() {
        _errorMessage = 'Payment failed: $e';
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Pay with ClickPesa',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Amount: ${CurrencyFormatter.format(widget.amount)}',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 24),
            
            // Payment Method Selection
            Text(
              'Select Payment Method',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            
            // Mobile Money Options
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildPaymentMethodChip(ClickPesaPaymentMethod.mpesa, 'M-Pesa'),
                _buildPaymentMethodChip(ClickPesaPaymentMethod.tigoPesa, 'Tigo Pesa'),
                _buildPaymentMethodChip(ClickPesaPaymentMethod.airtelMoney, 'Airtel Money'),
                _buildPaymentMethodChip(ClickPesaPaymentMethod.haloPesa, 'Halo Pesa'),
                _buildPaymentMethodChip(ClickPesaPaymentMethod.tPesa, 'T-Pesa'),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Phone Number Input
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone Number *',
                hintText: '+255 XXX XXX XXX or 0XXX XXX XXX',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
              enabled: !_isProcessing,
            ),
            
            const SizedBox(height: 16),
            
            // Error Message
            if (_errorMessage != null)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  border: Border.all(color: Colors.red.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red.shade600),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: TextStyle(color: Colors.red.shade600),
                      ),
                    ),
                  ],
                ),
              ),
            
            const SizedBox(height: 24),
            
            // Pay Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isProcessing ? null : _processPayment,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isProcessing
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 12),
                          Text('Processing Payment...'),
                        ],
                      )
                    : Text(
                        'Pay ${CurrencyFormatter.format(widget.amount)}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Info Text
            Text(
              'You will receive a payment prompt on your phone. Please follow the instructions to complete the payment.',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodChip(ClickPesaPaymentMethod method, String label) {
    final isSelected = _selectedPaymentMethod == method;
    
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: _isProcessing ? null : (selected) {
        setState(() {
          _selectedPaymentMethod = selected ? method : null;
          _errorMessage = null;
        });
      },
      selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }
}
