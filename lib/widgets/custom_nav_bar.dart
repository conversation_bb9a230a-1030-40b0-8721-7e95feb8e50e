import 'package:flutter/material.dart';

class CustomNavigationBar extends StatefulWidget {
  const CustomNavigationBar({
    super.key,
    this.selectedIndex = 0,
    this.onMenuSelected,
    required this.menus,
    this.backgroundColor,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  final int selectedIndex;
  final void Function(int)? onMenuSelected;
  final List<MenuItem> menus;
  final Color? backgroundColor;
  final Duration animationDuration;

  @override
  State<CustomNavigationBar> createState() => _CustomNavigationBarState();
}

class _CustomNavigationBarState extends State<CustomNavigationBar> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 8, right: 8, bottom: 24),
      width: double.infinity,
      height: 80,
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Theme.of(context).cardColor,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children:
            widget.menus.asMap().entries.map((entry) {
              int index = entry.key;
              MenuItem menu = entry.value;
              bool isSelected = widget.selectedIndex == index;

              return GestureDetector(
                onTap: () => widget.onMenuSelected?.call(index),
                child: AnimatedContainer(
                  duration: widget.animationDuration,
                  curve: Curves.easeInOut,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      AnimatedSwitcher(
                        duration: widget.animationDuration,
                        transitionBuilder: (child, animation) {
                          return ScaleTransition(
                            scale: animation,
                            child: child,
                          );
                        },
                        child:
                            isSelected && menu.activeIcon != null
                                ? KeyedSubtree(
                                  key: ValueKey('active_$index'),
                                  child: menu.activeIcon!,
                                )
                                : KeyedSubtree(
                                  key: ValueKey('inactive_$index'),
                                  child: menu.icon,
                                ),
                      ),
                      AnimatedSize(
                        duration: widget.animationDuration,
                        curve: Curves.easeInOut,
                        child:
                            isSelected
                                ? Row(
                                  children: [
                                    const SizedBox(width: 8),
                                    Text(
                                      menu.label,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                )
                                : const SizedBox.shrink(),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
      ),
    );
  }
}

class MenuItem {
  const MenuItem({required this.icon, required this.label, this.activeIcon});

  final Widget icon;
  final Widget? activeIcon;
  final String label;
}
