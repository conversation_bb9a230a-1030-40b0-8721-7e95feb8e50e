import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import '../../services/image_upload_service.dart';

/// Reusable image upload widget that can be used throughout the app
class ImageUploadWidget extends ConsumerStatefulWidget {
  final String? initialImageUrl;
  final String uploadFolder;
  final String? fileName;
  final Function(String imageUrl)? onImageUploaded;
  final Function(String error)? onError;
  final double height;
  final double width;
  final String placeholder;

  const ImageUploadWidget({
    super.key,
    this.initialImageUrl,
    required this.uploadFolder,
    this.fileName,
    this.onImageUploaded,
    this.onError,
    this.height = 200,
    this.width = double.infinity,
    this.placeholder = 'No image selected',
  });

  @override
  ConsumerState<ImageUploadWidget> createState() => _ImageUploadWidgetState();
}

class _ImageUploadWidgetState extends ConsumerState<ImageUploadWidget> {
  File? _selectedImage;
  String? _currentImageUrl;
  bool _isUploading = false;
  double _uploadProgress = 0.0;
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _currentImageUrl = widget.initialImageUrl;
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
        });
        await _uploadImage();
      }
    } catch (e) {
      _handleError('Failed to pick image: $e');
    }
  }

  Future<void> _uploadImage() async {
    if (_selectedImage == null) return;

    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
    });

    try {
      final imageUploadService = ref.read(imageUploadServiceProvider);

      // Validate image
      final validationResult = imageUploadService.validateImage(
        _selectedImage!,
      );
      if (!validationResult.isValid) {
        throw Exception(validationResult.error);
      }

      // Upload image
      final imageUrl = await imageUploadService.uploadImage(
        imageFile: _selectedImage!,
        folder: widget.uploadFolder,
        fileName: widget.fileName,
        onProgress: (progress) {
          if (mounted) {
            setState(() {
              _uploadProgress = progress;
            });
          }
        },
      );

      setState(() {
        _currentImageUrl = imageUrl;
        _selectedImage = null;
      });

      // Notify parent widget
      if (widget.onImageUploaded != null && imageUrl != null) {
        widget.onImageUploaded!(imageUrl);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Image uploaded successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _handleError('Failed to upload image: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
          _uploadProgress = 0.0;
        });
      }
    }
  }

  void _handleError(String error) {
    if (widget.onError != null) {
      widget.onError!(error);
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(error), backgroundColor: Colors.red),
      );
    }
  }

  void _showImageSourceDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Select Image Source'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('Gallery'),
                  onTap: () {
                    Navigator.pop(context);
                    _pickImage(ImageSource.gallery);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.camera_alt),
                  title: const Text('Camera'),
                  onTap: () {
                    Navigator.pop(context);
                    _pickImage(ImageSource.camera);
                  },
                ),
                if (_currentImageUrl != null)
                  ListTile(
                    leading: const Icon(Icons.delete, color: Colors.red),
                    title: const Text('Remove Image'),
                    onTap: () {
                      Navigator.pop(context);
                      setState(() {
                        _currentImageUrl = null;
                        _selectedImage = null;
                      });
                      if (widget.onImageUploaded != null) {
                        widget.onImageUploaded!('');
                      }
                    },
                  ),
              ],
            ),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      width: widget.width,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        children: [
          // Image display
          if (_selectedImage != null || _currentImageUrl != null)
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child:
                  _selectedImage != null
                      ? Image.file(
                        _selectedImage!,
                        width: widget.width,
                        height: widget.height,
                        fit: BoxFit.cover,
                      )
                      : Image.network(
                        _currentImageUrl!,
                        width: widget.width,
                        height: widget.height,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.error,
                                  size: 50,
                                  color: Colors.red,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Failed to load image',
                                  style: TextStyle(color: Colors.grey[600]),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
            )
          else
            // Placeholder
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.image, size: 50, color: Colors.grey[400]),
                  const SizedBox(height: 8),
                  Text(
                    widget.placeholder,
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: _isUploading ? null : _showImageSourceDialog,
                    icon: const Icon(Icons.photo_library),
                    label: const Text('Select Image'),
                  ),
                ],
              ),
            ),

          // Upload progress overlay
          if (_isUploading)
            Container(
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      value: _uploadProgress,
                      backgroundColor: Colors.white30,
                      valueColor: const AlwaysStoppedAnimation<Color>(
                        Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Uploading... ${(_uploadProgress * 100).toInt()}%',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // Action buttons overlay
          if (!_isUploading &&
              (_selectedImage != null || _currentImageUrl != null))
            Positioned(
              top: 8,
              right: 8,
              child: Row(
                children: [
                  CircleAvatar(
                    backgroundColor: Colors.blue,
                    child: IconButton(
                      icon: const Icon(
                        Icons.edit,
                        color: Colors.white,
                        size: 20,
                      ),
                      onPressed: _showImageSourceDialog,
                      tooltip: 'Change Image',
                    ),
                  ),
                  const SizedBox(width: 8),
                  CircleAvatar(
                    backgroundColor: Colors.red,
                    child: IconButton(
                      icon: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 20,
                      ),
                      onPressed: () {
                        setState(() {
                          _currentImageUrl = null;
                          _selectedImage = null;
                        });
                        if (widget.onImageUploaded != null) {
                          widget.onImageUploaded!('');
                        }
                      },
                      tooltip: 'Remove Image',
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
