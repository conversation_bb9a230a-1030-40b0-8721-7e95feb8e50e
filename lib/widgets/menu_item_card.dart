import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:smart_restaurant_menu/routes/routes.dart';
import 'package:smart_restaurant_menu/services/auth_service.dart';
import '../models/menu_item.dart';
import '../utils/currency_formatter.dart';

class MenuItemCard extends ConsumerStatefulWidget {
  final MenuItem item;

  const MenuItemCard({super.key, required this.item});

  @override
  ConsumerState<MenuItemCard> createState() => _MenuItemCardState();
}

class _MenuItemCardState extends ConsumerState<MenuItemCard> {
  @override
  Widget build(BuildContext context) {
    final item = widget.item;
    final user = ref.watch(authServiceProvider).user;
    final isFavorite = user?.favorites.contains(item.id) ?? false;
    return GestureDetector(
      onTap: () {
        context.goNamed(AppRoute.menuItem.name, extra: item);
      },
      child: Stack(
        children: [
          Container(
            margin: EdgeInsets.all(8.0),
            width: MediaQuery.sizeOf(context).width * 0.7,
            decoration: BoxDecoration(
              color: Colors.white,
              image: DecorationImage(
                image: NetworkImage(widget.item.imageUrl),
                fit: BoxFit.cover,
                colorFilter: ColorFilter.mode(
                  Colors.black.withOpacity(0.6),
                  BlendMode.darken,
                ),
              ),
              borderRadius: BorderRadius.circular(8.0),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 2,
                  blurRadius: 5,
                  offset: Offset(0, 3),
                ),
              ],
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.item.name,
                    style: TextStyle(
                      fontSize: 18.0,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: 4.0),
                  Text(
                    CurrencyFormatter.format(widget.item.price),
                    style: TextStyle(fontSize: 16.0, color: Colors.white),
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            top: 8.0,
            right: 8.0,
            child: IconButton(
              icon: Icon(
                isFavorite ? Icons.favorite : Icons.favorite_border,
                color: Colors.orange[700],
              ),
              onPressed: () async {
                if (user != null) {
                  await ref
                      .read(authServiceProvider.notifier)
                      .toggleFavorite(item.id);
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
