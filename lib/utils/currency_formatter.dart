// File: lib/utils/currency_formatter.dart
import 'package:intl/intl.dart';

/// Utility class for formatting currency in Tanzanian Shillings (TZS)
class CurrencyFormatter {
  // Private constructor to prevent instantiation
  CurrencyFormatter._();

  /// Currency code for Tanzanian Shillings
  static const String currencyCode = 'TZS';
  
  /// Currency symbol for Tanzanian Shillings
  static const String currencySymbol = 'TZS';

  /// Number formatter for Tanzanian locale
  static final NumberFormat _formatter = NumberFormat.currency(
    locale: 'sw_TZ', // Swahili (Tanzania) locale
    symbol: currencySymbol,
    decimalDigits: 2,
  );

  /// Simple formatter without locale (fallback)
  static final NumberFormat _simpleFormatter = NumberFormat.currency(
    symbol: currencySymbol,
    decimalDigits: 2,
  );

  /// Format amount as Tanzanian Shillings with proper locale
  static String format(double amount) {
    try {
      return _formatter.format(amount);
    } catch (e) {
      // Fallback to simple formatter if locale is not available
      return _simpleFormatter.format(amount);
    }
  }

  /// Format amount as Tanzanian Shillings without decimals for whole numbers
  static String formatCompact(double amount) {
    if (amount == amount.roundToDouble()) {
      // It's a whole number, format without decimals
      try {
        final formatter = NumberFormat.currency(
          locale: 'sw_TZ',
          symbol: currencySymbol,
          decimalDigits: 0,
        );
        return formatter.format(amount);
      } catch (e) {
        return '$currencySymbol ${amount.toStringAsFixed(0)}';
      }
    } else {
      return format(amount);
    }
  }

  /// Format large amounts with K/M suffixes (e.g., TZS 1.5K, TZS 2.3M)
  static String formatLarge(double amount) {
    if (amount >= 1000000) {
      return '$currencySymbol ${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '$currencySymbol ${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return formatCompact(amount);
    }
  }

  /// Format amount for display in forms (without currency symbol)
  static String formatForInput(double amount) {
    return amount.toStringAsFixed(2);
  }

  /// Parse string to double (for form inputs)
  static double? parseAmount(String value) {
    if (value.isEmpty) return null;
    
    // Remove currency symbols and spaces
    final cleanValue = value
        .replaceAll(currencySymbol, '')
        .replaceAll(',', '')
        .trim();
    
    return double.tryParse(cleanValue);
  }

  /// Validate currency input
  static bool isValidAmount(String value) {
    final amount = parseAmount(value);
    return amount != null && amount >= 0;
  }

  /// Format amount for API calls (usually as string with 2 decimal places)
  static String formatForApi(double amount) {
    return amount.toStringAsFixed(2);
  }

  /// Get currency info for payment gateways
  static Map<String, String> getCurrencyInfo() {
    return {
      'code': currencyCode,
      'symbol': currencySymbol,
      'name': 'Tanzanian Shilling',
      'country': 'Tanzania',
    };
  }

  /// Format price range (e.g., "TZS 1,000 - TZS 5,000")
  static String formatRange(double minAmount, double maxAmount) {
    return '${format(minAmount)} - ${format(maxAmount)}';
  }

  /// Format with custom decimal places
  static String formatWithDecimals(double amount, int decimalPlaces) {
    try {
      final formatter = NumberFormat.currency(
        locale: 'sw_TZ',
        symbol: currencySymbol,
        decimalDigits: decimalPlaces,
      );
      return formatter.format(amount);
    } catch (e) {
      return '$currencySymbol ${amount.toStringAsFixed(decimalPlaces)}';
    }
  }

  /// Check if amount is in valid Tanzanian Shilling range
  static bool isValidTanzanianAmount(double amount) {
    // Minimum: 100 TZS (reasonable minimum for transactions)
    // Maximum: 100,000,000 TZS (100 million - reasonable maximum)
    return amount >= 100 && amount <= 100000000;
  }

  /// Get formatted string for mobile money amounts (whole numbers preferred)
  static String formatForMobileMoney(double amount) {
    // Mobile money in Tanzania typically works with whole numbers
    if (amount == amount.roundToDouble()) {
      return '$currencySymbol ${amount.toStringAsFixed(0)}';
    } else {
      return format(amount);
    }
  }

  /// Convert amount to cents/minor units (for some payment APIs)
  static int toMinorUnits(double amount) {
    return (amount * 100).round();
  }

  /// Convert from cents/minor units to major units
  static double fromMinorUnits(int minorUnits) {
    return minorUnits / 100.0;
  }
}
