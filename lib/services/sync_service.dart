import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/order.dart';
import 'offline_service.dart';
import 'order_service.dart';
import 'crashlytics_service.dart';
import 'database_service.dart';

class SyncService {
  final Ref _ref;
  Timer? _syncTimer;
  bool _isSyncing = false;

  SyncService(this._ref) {
    _startPeriodicSync();
    _listenToConnectivity();
  }

  // Start periodic sync every 5 minutes
  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      _syncPendingData();
    });
  }

  // Listen to connectivity changes and sync when online
  void _listenToConnectivity() {
    // Note: Using ref.listen for provider changes
    _ref.listen(connectivityProvider, (previous, next) {
      next.whenData((isOnline) {
        if (isOnline && !_isSyncing) {
          _syncPendingData();
        }
      });
    });
  }

  // Sync all pending offline data
  Future<void> _syncPendingData() async {
    if (_isSyncing) return;

    _isSyncing = true;

    try {
      await _ref
          .read(crashlyticsServiceProvider)
          .recordUserAction('sync_started');

      final offlineService = _ref.read(offlineServiceProvider);
      final isOnline = await offlineService.isOnline();

      if (!isOnline) {
        _isSyncing = false;
        return;
      }

      final pendingData = await offlineService.getPendingOfflineData();

      if (pendingData.isEmpty) {
        _isSyncing = false;
        return;
      }

      int syncedCount = 0;
      int failedCount = 0;

      for (final data in pendingData) {
        try {
          await offlineService.updateOfflineDataStatus(
            data.id,
            SyncStatus.syncing,
          );

          bool syncSuccess = false;

          switch (data.type) {
            case OfflineDataType.orders:
              syncSuccess = await _syncOrder(data);
              break;
            case OfflineDataType.userProfile:
              syncSuccess = await _syncUserProfile(data);
              break;
            case OfflineDataType.favorites:
              syncSuccess = await _syncFavorites(data);
              break;
            case OfflineDataType.cartData:
              // Cart data doesn't need server sync, just mark as synced
              syncSuccess = true;
              break;
            case OfflineDataType.menuItems:
              // Menu items are read-only, no sync needed
              syncSuccess = true;
              break;
          }

          if (syncSuccess) {
            await offlineService.updateOfflineDataStatus(
              data.id,
              SyncStatus.synced,
            );
            syncedCount++;
          } else {
            await offlineService.updateOfflineDataStatus(
              data.id,
              SyncStatus.failed,
            );
            failedCount++;
          }
        } catch (e) {
          await offlineService.updateOfflineDataStatus(
            data.id,
            SyncStatus.failed,
          );
          failedCount++;

          await _ref
              .read(crashlyticsServiceProvider)
              .recordError(
                e,
                StackTrace.current,
                reason: 'Failed to sync data: ${data.id}',
              );
        }
      }

      await _ref
          .read(crashlyticsServiceProvider)
          .recordUserAction(
            'sync_completed',
            parameters: {
              'syncedCount': syncedCount,
              'failedCount': failedCount,
              'totalCount': pendingData.length,
            },
          );
    } catch (e) {
      await _ref
          .read(crashlyticsServiceProvider)
          .recordError(
            e,
            StackTrace.current,
            reason: 'Failed to sync pending data',
          );
    } finally {
      _isSyncing = false;
    }
  }

  // Sync order data
  Future<bool> _syncOrder(OfflineData data) async {
    try {
      final orderData = data.data;
      final orderService = _ref.read(orderServiceProvider);

      // Check if this is a new order or an update
      if (orderData.containsKey('isNew') && orderData['isNew'] == true) {
        // Create new order
        final order = OrderModel.fromMap(orderData);
        await orderService.createOrder(order);
      } else {
        // Update existing order
        final orderId = orderData['id'] as String;
        final status = OrderStatus.values.firstWhere(
          (s) => s.name == orderData['status'],
          orElse: () => OrderStatus.pending,
        );

        await orderService.updateOrderStatus(
          orderId: orderId,
          newStatus: status,
          note: orderData['note'],
        );
      }

      return true;
    } catch (e) {
      await _ref
          .read(crashlyticsServiceProvider)
          .recordError(e, StackTrace.current, reason: 'Failed to sync order');
      return false;
    }
  }

  // Sync user profile data
  Future<bool> _syncUserProfile(OfflineData data) async {
    try {
      final profileData = data.data;
      final databaseService = _ref.read(databaseServiceProvider);

      // Update user profile in Firestore
      await databaseService.updateUserProfile(profileData);

      return true;
    } catch (e) {
      await _ref
          .read(crashlyticsServiceProvider)
          .recordError(
            e,
            StackTrace.current,
            reason: 'Failed to sync user profile',
          );
      return false;
    }
  }

  // Sync favorites data
  Future<bool> _syncFavorites(OfflineData data) async {
    try {
      final favoritesData = data.data;
      final databaseService = _ref.read(databaseServiceProvider);

      // Update favorites in Firestore
      await databaseService.updateUserFavorites(favoritesData);

      return true;
    } catch (e) {
      await _ref
          .read(crashlyticsServiceProvider)
          .recordError(
            e,
            StackTrace.current,
            reason: 'Failed to sync favorites',
          );
      return false;
    }
  }

  // Force sync now
  Future<void> forceSyncNow() async {
    await _syncPendingData();
  }

  // Get sync status
  Future<Map<String, dynamic>> getSyncStatus() async {
    try {
      final offlineService = _ref.read(offlineServiceProvider);
      final pendingData = await offlineService.getPendingOfflineData();
      final isOnline = await offlineService.isOnline();

      final pendingByType = <String, int>{};
      for (final data in pendingData) {
        final typeName = data.type.name;
        pendingByType[typeName] = (pendingByType[typeName] ?? 0) + 1;
      }

      return {
        'isOnline': isOnline,
        'isSyncing': _isSyncing,
        'totalPending': pendingData.length,
        'pendingByType': pendingByType,
        'lastSyncAttempt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'isOnline': false,
        'isSyncing': false,
        'totalPending': 0,
        'pendingByType': <String, int>{},
        'error': e.toString(),
      };
    }
  }

  // Queue data for sync
  Future<void> queueForSync({
    required String id,
    required OfflineDataType type,
    required Map<String, dynamic> data,
  }) async {
    try {
      final offlineService = _ref.read(offlineServiceProvider);
      final offlineData = OfflineData(
        id: id,
        type: type,
        data: data,
        lastModified: DateTime.now(),
        syncStatus: SyncStatus.pending,
      );

      await offlineService.storeOfflineData(offlineData);

      // Try to sync immediately if online
      final isOnline = await offlineService.isOnline();
      if (isOnline && !_isSyncing) {
        _syncPendingData();
      }
    } catch (e) {
      await _ref
          .read(crashlyticsServiceProvider)
          .recordError(
            e,
            StackTrace.current,
            reason: 'Failed to queue data for sync',
          );
    }
  }

  // Clear sync queue
  Future<void> clearSyncQueue() async {
    try {
      final offlineService = _ref.read(offlineServiceProvider);
      final pendingData = await offlineService.getPendingOfflineData();

      for (final data in pendingData) {
        await offlineService.updateOfflineDataStatus(
          data.id,
          SyncStatus.synced,
        );
      }

      await _ref
          .read(crashlyticsServiceProvider)
          .recordUserAction(
            'sync_queue_cleared',
            parameters: {'clearedCount': pendingData.length},
          );
    } catch (e) {
      await _ref
          .read(crashlyticsServiceProvider)
          .recordError(
            e,
            StackTrace.current,
            reason: 'Failed to clear sync queue',
          );
    }
  }

  // Retry failed syncs
  Future<void> retryFailedSyncs() async {
    try {
      final offlineService = _ref.read(offlineServiceProvider);
      final pendingData = await offlineService.getPendingOfflineData();

      final failedData = pendingData.where(
        (data) => data.syncStatus == SyncStatus.failed,
      );

      for (final data in failedData) {
        await offlineService.updateOfflineDataStatus(
          data.id,
          SyncStatus.pending,
        );
      }

      // Trigger sync
      if (failedData.isNotEmpty) {
        _syncPendingData();
      }

      await _ref
          .read(crashlyticsServiceProvider)
          .recordUserAction(
            'failed_syncs_retried',
            parameters: {'retryCount': failedData.length},
          );
    } catch (e) {
      await _ref
          .read(crashlyticsServiceProvider)
          .recordError(
            e,
            StackTrace.current,
            reason: 'Failed to retry failed syncs',
          );
    }
  }

  // Dispose resources
  void dispose() {
    _syncTimer?.cancel();
  }
}

// Riverpod provider for SyncService
final syncServiceProvider = Provider<SyncService>((ref) {
  final service = SyncService(ref);
  ref.onDispose(() => service.dispose());
  return service;
});

// Sync status provider
final syncStatusProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  return await ref.read(syncServiceProvider).getSyncStatus();
});
