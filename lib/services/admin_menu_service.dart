import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/menu_item.dart';

class AdminMenuService {
  final FirebaseFirestore _db = FirebaseFirestore.instance;

  // Get real-time stream of all menu items
  Stream<List<MenuItem>> getAllMenuItemsStream() {
    return _db
        .collection('menu_items')
        .orderBy('category')
        .orderBy('name')
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => MenuItem.fromMap(doc.data()))
          .toList();
    });
  }

  // Get menu items by category
  Stream<List<MenuItem>> getMenuItemsByCategoryStream(String category) {
    return _db
        .collection('menu_items')
        .where('category', isEqualTo: category)
        .orderBy('name')
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => MenuItem.fromMap(doc.data()))
          .toList();
    });
  }

  // Get available menu items only
  Stream<List<MenuItem>> getAvailableMenuItemsStream() {
    return _db
        .collection('menu_items')
        .where('isAvailable', isEqualTo: true)
        .orderBy('category')
        .orderBy('name')
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => MenuItem.fromMap(doc.data()))
          .toList();
    });
  }

  // Get all categories
  Future<List<String>> getCategories() async {
    try {
      final snapshot = await _db.collection('menu_items').get();
      final categories = <String>{};
      
      for (final doc in snapshot.docs) {
        final category = doc.data()['category'] as String?;
        if (category != null && category.isNotEmpty) {
          categories.add(category);
        }
      }
      
      final categoryList = categories.toList();
      categoryList.sort();
      return categoryList;
    } catch (e) {
      throw Exception('Failed to get categories: $e');
    }
  }

  // Create new menu item
  Future<void> createMenuItem(MenuItem item) async {
    try {
      await _db.collection('menu_items').doc(item.id).set(item.toMap());
      
      // Log activity
      await _logMenuActivity(
        action: 'create_menu_item',
        itemId: item.id,
        details: {
          'name': item.name,
          'category': item.category,
          'price': item.price,
        },
      );
    } catch (e) {
      throw Exception('Failed to create menu item: $e');
    }
  }

  // Update existing menu item
  Future<void> updateMenuItem(MenuItem item) async {
    try {
      await _db.collection('menu_items').doc(item.id).update(item.toMap());
      
      // Log activity
      await _logMenuActivity(
        action: 'update_menu_item',
        itemId: item.id,
        details: {
          'name': item.name,
          'category': item.category,
          'price': item.price,
          'isAvailable': item.isAvailable,
        },
      );
    } catch (e) {
      throw Exception('Failed to update menu item: $e');
    }
  }

  // Delete menu item
  Future<void> deleteMenuItem(String itemId) async {
    try {
      // Get item details before deletion for logging
      final doc = await _db.collection('menu_items').doc(itemId).get();
      final itemData = doc.data();
      
      await _db.collection('menu_items').doc(itemId).delete();
      
      // Log activity
      await _logMenuActivity(
        action: 'delete_menu_item',
        itemId: itemId,
        details: {
          'name': itemData?['name'] ?? 'Unknown',
          'category': itemData?['category'] ?? 'Unknown',
        },
      );
    } catch (e) {
      throw Exception('Failed to delete menu item: $e');
    }
  }

  // Toggle item availability
  Future<void> toggleItemAvailability(String itemId, bool isAvailable) async {
    try {
      await _db.collection('menu_items').doc(itemId).update({
        'isAvailable': isAvailable,
        'updatedAt': DateTime.now().toIso8601String(),
      });
      
      // Log activity
      await _logMenuActivity(
        action: 'toggle_availability',
        itemId: itemId,
        details: {
          'isAvailable': isAvailable,
        },
      );
    } catch (e) {
      throw Exception('Failed to toggle item availability: $e');
    }
  }

  // Bulk update availability
  Future<void> bulkUpdateAvailability({
    required List<String> itemIds,
    required bool isAvailable,
  }) async {
    final batch = _db.batch();
    
    try {
      for (final itemId in itemIds) {
        final itemRef = _db.collection('menu_items').doc(itemId);
        batch.update(itemRef, {
          'isAvailable': isAvailable,
          'updatedAt': DateTime.now().toIso8601String(),
        });
      }
      
      await batch.commit();
      
      // Log bulk activity
      await _logMenuActivity(
        action: 'bulk_update_availability',
        itemId: 'multiple',
        details: {
          'itemIds': itemIds,
          'isAvailable': isAvailable,
          'count': itemIds.length,
        },
      );
    } catch (e) {
      throw Exception('Failed to bulk update availability: $e');
    }
  }

  // Update category for multiple items
  Future<void> bulkUpdateCategory({
    required List<String> itemIds,
    required String newCategory,
  }) async {
    final batch = _db.batch();
    
    try {
      for (final itemId in itemIds) {
        final itemRef = _db.collection('menu_items').doc(itemId);
        batch.update(itemRef, {
          'category': newCategory,
          'updatedAt': DateTime.now().toIso8601String(),
        });
      }
      
      await batch.commit();
      
      // Log bulk activity
      await _logMenuActivity(
        action: 'bulk_update_category',
        itemId: 'multiple',
        details: {
          'itemIds': itemIds,
          'newCategory': newCategory,
          'count': itemIds.length,
        },
      );
    } catch (e) {
      throw Exception('Failed to bulk update category: $e');
    }
  }

  // Search menu items
  Future<List<MenuItem>> searchMenuItems({
    String? query,
    String? category,
    bool? isAvailable,
    double? minPrice,
    double? maxPrice,
  }) async {
    try {
      Query menuQuery = _db.collection('menu_items');

      // Filter by category
      if (category != null && category.isNotEmpty) {
        menuQuery = menuQuery.where('category', isEqualTo: category);
      }

      // Filter by availability
      if (isAvailable != null) {
        menuQuery = menuQuery.where('isAvailable', isEqualTo: isAvailable);
      }

      menuQuery = menuQuery.orderBy('name');

      final snapshot = await menuQuery.get();
      List<MenuItem> items = snapshot.docs
          .map((doc) => MenuItem.fromMap(doc.data() as Map<String, dynamic>))
          .toList();

      // Apply text search filter
      if (query != null && query.isNotEmpty) {
        items = items.where((item) {
          return item.name.toLowerCase().contains(query.toLowerCase()) ||
              item.description.toLowerCase().contains(query.toLowerCase()) ||
              item.category.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }

      // Apply price range filter
      if (minPrice != null) {
        items = items.where((item) => item.price >= minPrice).toList();
      }
      if (maxPrice != null) {
        items = items.where((item) => item.price <= maxPrice).toList();
      }

      return items;
    } catch (e) {
      throw Exception('Failed to search menu items: $e');
    }
  }

  // Get menu statistics
  Future<Map<String, dynamic>> getMenuStatistics() async {
    try {
      final snapshot = await _db.collection('menu_items').get();
      final items = snapshot.docs
          .map((doc) => MenuItem.fromMap(doc.data()))
          .toList();

      // Calculate statistics
      final totalItems = items.length;
      final availableItems = items.where((item) => item.isAvailable).length;
      final unavailableItems = totalItems - availableItems;

      // Category breakdown
      final categoryStats = <String, int>{};
      for (final item in items) {
        categoryStats[item.category] = (categoryStats[item.category] ?? 0) + 1;
      }

      // Price statistics
      final prices = items.map((item) => item.price).toList();
      prices.sort();
      
      final averagePrice = prices.isNotEmpty 
          ? prices.reduce((a, b) => a + b) / prices.length 
          : 0.0;
      final minPrice = prices.isNotEmpty ? prices.first : 0.0;
      final maxPrice = prices.isNotEmpty ? prices.last : 0.0;

      return {
        'totalItems': totalItems,
        'availableItems': availableItems,
        'unavailableItems': unavailableItems,
        'categoryStats': categoryStats,
        'averagePrice': averagePrice,
        'minPrice': minPrice,
        'maxPrice': maxPrice,
        'categories': categoryStats.keys.toList(),
      };
    } catch (e) {
      throw Exception('Failed to get menu statistics: $e');
    }
  }

  // Log menu activity
  Future<void> _logMenuActivity({
    required String action,
    required String itemId,
    required Map<String, dynamic> details,
  }) async {
    try {
      await _db.collection('menu_activities').add({
        'action': action,
        'itemId': itemId,
        'details': details,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      // Log activity failure shouldn't break the main operation
      print('Failed to log menu activity: $e');
    }
  }

  // Get menu activities
  Stream<List<Map<String, dynamic>>> getMenuActivitiesStream({
    int limit = 50,
  }) {
    return _db
        .collection('menu_activities')
        .orderBy('timestamp', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => {
                'id': doc.id,
                ...doc.data(),
              })
          .toList();
    });
  }
}

// Riverpod providers
final adminMenuServiceProvider = Provider<AdminMenuService>((ref) {
  return AdminMenuService();
});

// Stream providers for real-time data
final allMenuItemsStreamProvider = StreamProvider<List<MenuItem>>((ref) {
  return ref.read(adminMenuServiceProvider).getAllMenuItemsStream();
});

final availableMenuItemsStreamProvider = StreamProvider<List<MenuItem>>((ref) {
  return ref.read(adminMenuServiceProvider).getAvailableMenuItemsStream();
});

final menuCategoriesProvider = FutureProvider<List<String>>((ref) {
  return ref.read(adminMenuServiceProvider).getCategories();
});

final menuStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) {
  return ref.read(adminMenuServiceProvider).getMenuStatistics();
});

final menuActivitiesStreamProvider = StreamProvider<List<Map<String, dynamic>>>((ref) {
  return ref.read(adminMenuServiceProvider).getMenuActivitiesStream();
});
