import 'dart:convert';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import '../models/menu_item.dart';
import '../models/order.dart';
import 'crashlytics_service.dart';
import 'secure_storage_service.dart';

// Offline data types
enum OfflineDataType { menuItems, orders, userProfile, cartData, favorites }

// Sync status
enum SyncStatus { synced, pending, failed, syncing }

// Offline data model
class OfflineData {
  final String id;
  final OfflineDataType type;
  final Map<String, dynamic> data;
  final DateTime lastModified;
  final SyncStatus syncStatus;

  OfflineData({
    required this.id,
    required this.type,
    required this.data,
    required this.lastModified,
    this.syncStatus = SyncStatus.pending,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.name,
      'data': data,
      'lastModified': lastModified.toIso8601String(),
      'syncStatus': syncStatus.name,
    };
  }

  factory OfflineData.fromMap(Map<String, dynamic> map) {
    return OfflineData(
      id: map['id'],
      type: OfflineDataType.values.firstWhere((e) => e.name == map['type']),
      data: Map<String, dynamic>.from(map['data']),
      lastModified: DateTime.parse(map['lastModified']),
      syncStatus: SyncStatus.values.firstWhere(
        (e) => e.name == map['syncStatus'],
        orElse: () => SyncStatus.pending,
      ),
    );
  }

  OfflineData copyWith({
    String? id,
    OfflineDataType? type,
    Map<String, dynamic>? data,
    DateTime? lastModified,
    SyncStatus? syncStatus,
  }) {
    return OfflineData(
      id: id ?? this.id,
      type: type ?? this.type,
      data: data ?? this.data,
      lastModified: lastModified ?? this.lastModified,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }
}

class OfflineService {
  final Ref _ref;
  final Connectivity _connectivity = Connectivity();

  // Cache file paths
  static const String _menuCacheFile = 'menu_cache.json';
  static const String _ordersCacheFile = 'orders_cache.json';
  static const String _offlineDataFile = 'offline_data.json';

  OfflineService(this._ref);

  // Check connectivity status
  Future<bool> isOnline() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();

      if (connectivityResult.contains(ConnectivityResult.none)) {
        return false;
      }

      // Additional check by trying to reach a reliable server
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      await _ref
          .read(crashlyticsServiceProvider)
          .recordError(
            e,
            StackTrace.current,
            reason: 'Failed to check connectivity',
          );
      return false;
    }
  }

  // Get connectivity stream
  Stream<bool> get connectivityStream {
    return _connectivity.onConnectivityChanged.asyncMap((_) async {
      return await isOnline();
    });
  }

  // Cache menu items
  Future<void> cacheMenuItems(List<MenuItem> menuItems) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$_menuCacheFile');

      final menuData = menuItems.map((item) => item.toMap()).toList();
      final cacheData = {
        'data': menuData,
        'timestamp': DateTime.now().toIso8601String(),
        'version': '1.0',
      };

      await file.writeAsString(jsonEncode(cacheData));

      await _ref
          .read(crashlyticsServiceProvider)
          .recordUserAction(
            'menu_cached',
            parameters: {'itemCount': menuItems.length},
          );
    } catch (e) {
      await _ref
          .read(crashlyticsServiceProvider)
          .recordError(
            e,
            StackTrace.current,
            reason: 'Failed to cache menu items',
          );
    }
  }

  // Get cached menu items
  Future<List<MenuItem>?> getCachedMenuItems() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$_menuCacheFile');

      if (!await file.exists()) {
        return null;
      }

      final content = await file.readAsString();
      final cacheData = jsonDecode(content) as Map<String, dynamic>;

      // Check if cache is still valid (24 hours)
      final timestamp = DateTime.parse(cacheData['timestamp']);
      final now = DateTime.now();
      if (now.difference(timestamp).inHours > 24) {
        await file.delete();
        return null;
      }

      final menuData = cacheData['data'] as List<dynamic>;
      return menuData.map((item) {
        final itemMap = item as Map<String, dynamic>;
        return MenuItem.fromMap(itemMap, itemMap['id'] ?? '');
      }).toList();
    } catch (e) {
      await _ref
          .read(crashlyticsServiceProvider)
          .recordError(
            e,
            StackTrace.current,
            reason: 'Failed to get cached menu items',
          );
      return null;
    }
  }

  // Cache user orders
  Future<void> cacheOrders(List<OrderModel> orders) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$_ordersCacheFile');

      final ordersData = orders.map((order) => order.toMap()).toList();
      final cacheData = {
        'data': ordersData,
        'timestamp': DateTime.now().toIso8601String(),
        'version': '1.0',
      };

      await file.writeAsString(jsonEncode(cacheData));

      await _ref
          .read(crashlyticsServiceProvider)
          .recordUserAction(
            'orders_cached',
            parameters: {'orderCount': orders.length},
          );
    } catch (e) {
      await _ref
          .read(crashlyticsServiceProvider)
          .recordError(e, StackTrace.current, reason: 'Failed to cache orders');
    }
  }

  // Get cached orders
  Future<List<OrderModel>?> getCachedOrders() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$_ordersCacheFile');

      if (!await file.exists()) {
        return null;
      }

      final content = await file.readAsString();
      final cacheData = jsonDecode(content) as Map<String, dynamic>;

      final ordersData = cacheData['data'] as List<dynamic>;
      return ordersData
          .map((order) => OrderModel.fromMap(order as Map<String, dynamic>))
          .toList();
    } catch (e) {
      await _ref
          .read(crashlyticsServiceProvider)
          .recordError(
            e,
            StackTrace.current,
            reason: 'Failed to get cached orders',
          );
      return null;
    }
  }

  // Store offline data for later sync
  Future<void> storeOfflineData(OfflineData offlineData) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$_offlineDataFile');

      List<OfflineData> existingData = [];
      if (await file.exists()) {
        final content = await file.readAsString();
        final dataList = jsonDecode(content) as List<dynamic>;
        existingData =
            dataList.map((item) => OfflineData.fromMap(item)).toList();
      }

      // Remove existing data with same ID
      existingData.removeWhere((item) => item.id == offlineData.id);

      // Add new data
      existingData.add(offlineData);

      // Save back to file
      final dataToSave = existingData.map((item) => item.toMap()).toList();
      await file.writeAsString(jsonEncode(dataToSave));

      await _ref
          .read(crashlyticsServiceProvider)
          .recordUserAction(
            'offline_data_stored',
            parameters: {'type': offlineData.type.name, 'id': offlineData.id},
          );
    } catch (e) {
      await _ref
          .read(crashlyticsServiceProvider)
          .recordError(
            e,
            StackTrace.current,
            reason: 'Failed to store offline data',
          );
    }
  }

  // Get pending offline data
  Future<List<OfflineData>> getPendingOfflineData() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$_offlineDataFile');

      if (!await file.exists()) {
        return [];
      }

      final content = await file.readAsString();
      final dataList = jsonDecode(content) as List<dynamic>;
      final offlineData =
          dataList.map((item) => OfflineData.fromMap(item)).toList();

      return offlineData
          .where(
            (item) =>
                item.syncStatus == SyncStatus.pending ||
                item.syncStatus == SyncStatus.failed,
          )
          .toList();
    } catch (e) {
      await _ref
          .read(crashlyticsServiceProvider)
          .recordError(
            e,
            StackTrace.current,
            reason: 'Failed to get pending offline data',
          );
      return [];
    }
  }

  // Update offline data sync status
  Future<void> updateOfflineDataStatus(String id, SyncStatus status) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$_offlineDataFile');

      if (!await file.exists()) {
        return;
      }

      final content = await file.readAsString();
      final dataList = jsonDecode(content) as List<dynamic>;
      final offlineData =
          dataList.map((item) => OfflineData.fromMap(item)).toList();

      final index = offlineData.indexWhere((item) => item.id == id);
      if (index != -1) {
        offlineData[index] = offlineData[index].copyWith(syncStatus: status);

        // Remove synced items
        if (status == SyncStatus.synced) {
          offlineData.removeAt(index);
        }

        final dataToSave = offlineData.map((item) => item.toMap()).toList();
        await file.writeAsString(jsonEncode(dataToSave));
      }
    } catch (e) {
      await _ref
          .read(crashlyticsServiceProvider)
          .recordError(
            e,
            StackTrace.current,
            reason: 'Failed to update offline data status',
          );
    }
  }

  // Clear cache
  Future<void> clearCache() async {
    try {
      final directory = await getApplicationDocumentsDirectory();

      final menuFile = File('${directory.path}/$_menuCacheFile');
      if (await menuFile.exists()) {
        await menuFile.delete();
      }

      final ordersFile = File('${directory.path}/$_ordersCacheFile');
      if (await ordersFile.exists()) {
        await ordersFile.delete();
      }

      await _ref
          .read(crashlyticsServiceProvider)
          .recordUserAction('cache_cleared');
    } catch (e) {
      await _ref
          .read(crashlyticsServiceProvider)
          .recordError(e, StackTrace.current, reason: 'Failed to clear cache');
    }
  }

  // Get cache size
  Future<int> getCacheSize() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      int totalSize = 0;

      final menuFile = File('${directory.path}/$_menuCacheFile');
      if (await menuFile.exists()) {
        totalSize += await menuFile.length();
      }

      final ordersFile = File('${directory.path}/$_ordersCacheFile');
      if (await ordersFile.exists()) {
        totalSize += await ordersFile.length();
      }

      final offlineFile = File('${directory.path}/$_offlineDataFile');
      if (await offlineFile.exists()) {
        totalSize += await offlineFile.length();
      }

      return totalSize;
    } catch (e) {
      return 0;
    }
  }

  // Format cache size for display
  String formatCacheSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }
}

// Riverpod provider for OfflineService
final offlineServiceProvider = Provider<OfflineService>((ref) {
  return OfflineService(ref);
});

// Connectivity provider
final connectivityProvider = StreamProvider<bool>((ref) {
  return ref.read(offlineServiceProvider).connectivityStream;
});
