import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/restaurant_settings.dart';
import '../models/user.dart';

class SettingsService {
  final FirebaseFirestore _db = FirebaseFirestore.instance;
  static const String _settingsCollection = 'restaurant_settings';
  static const String _defaultSettingsId = 'default';

  // Get restaurant settings
  Future<RestaurantSettings> getRestaurantSettings() async {
    try {
      final doc = await _db
          .collection(_settingsCollection)
          .doc(_defaultSettingsId)
          .get();

      if (doc.exists) {
        return RestaurantSettings.fromMap(doc.data()!);
      } else {
        // Create default settings if none exist
        final defaultSettings = _createDefaultSettings();
        await _db
            .collection(_settingsCollection)
            .doc(_defaultSettingsId)
            .set(defaultSettings.toMap());
        return defaultSettings;
      }
    } catch (e) {
      throw Exception('Failed to get restaurant settings: $e');
    }
  }

  // Get restaurant settings stream
  Stream<RestaurantSettings> getRestaurantSettingsStream() {
    return _db
        .collection(_settingsCollection)
        .doc(_defaultSettingsId)
        .snapshots()
        .map((doc) {
      if (doc.exists) {
        return RestaurantSettings.fromMap(doc.data()!);
      } else {
        return _createDefaultSettings();
      }
    });
  }

  // Update restaurant settings
  Future<void> updateRestaurantSettings(RestaurantSettings settings) async {
    try {
      final updatedSettings = settings.copyWith(
        updatedAt: DateTime.now(),
      );

      await _db
          .collection(_settingsCollection)
          .doc(_defaultSettingsId)
          .set(updatedSettings.toMap());

      // Log activity
      await _logSettingsActivity(
        action: 'update_restaurant_settings',
        details: {
          'settingsId': settings.id,
          'name': settings.name,
        },
      );
    } catch (e) {
      throw Exception('Failed to update restaurant settings: $e');
    }
  }

  // Update specific setting sections
  Future<void> updateBasicInfo({
    required String name,
    required String description,
    required String email,
    required String phone,
    required String address,
    required String city,
    required String country,
  }) async {
    try {
      final currentSettings = await getRestaurantSettings();
      final updatedSettings = currentSettings.copyWith(
        name: name,
        description: description,
        email: email,
        phone: phone,
        address: address,
        city: city,
        country: country,
        updatedAt: DateTime.now(),
      );

      await updateRestaurantSettings(updatedSettings);
    } catch (e) {
      throw Exception('Failed to update basic info: $e');
    }
  }

  Future<void> updateOperatingHours(Map<String, OperatingHours> operatingHours) async {
    try {
      final currentSettings = await getRestaurantSettings();
      final updatedSettings = currentSettings.copyWith(
        operatingHours: operatingHours,
        updatedAt: DateTime.now(),
      );

      await updateRestaurantSettings(updatedSettings);
    } catch (e) {
      throw Exception('Failed to update operating hours: $e');
    }
  }

  Future<void> updatePaymentSettings(PaymentSettings paymentSettings) async {
    try {
      final currentSettings = await getRestaurantSettings();
      final updatedSettings = currentSettings.copyWith(
        paymentSettings: paymentSettings,
        updatedAt: DateTime.now(),
      );

      await updateRestaurantSettings(updatedSettings);
    } catch (e) {
      throw Exception('Failed to update payment settings: $e');
    }
  }

  Future<void> updateNotificationSettings(NotificationSettings notificationSettings) async {
    try {
      final currentSettings = await getRestaurantSettings();
      final updatedSettings = currentSettings.copyWith(
        notificationSettings: notificationSettings,
        updatedAt: DateTime.now(),
      );

      await updateRestaurantSettings(updatedSettings);
    } catch (e) {
      throw Exception('Failed to update notification settings: $e');
    }
  }

  Future<void> updateAppSettings(AppSettings appSettings) async {
    try {
      final currentSettings = await getRestaurantSettings();
      final updatedSettings = currentSettings.copyWith(
        appSettings: appSettings,
        updatedAt: DateTime.now(),
      );

      await updateRestaurantSettings(updatedSettings);
    } catch (e) {
      throw Exception('Failed to update app settings: $e');
    }
  }

  // Staff management
  Future<List<AppUser>> getAllStaff() async {
    try {
      final snapshot = await _db
          .collection('users')
          .where('role', whereIn: [
            UserRole.staff.name,
            UserRole.manager.name,
            UserRole.admin.name,
          ])
          .get();

      return snapshot.docs
          .map((doc) => AppUser.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('Failed to get staff list: $e');
    }
  }

  Stream<List<AppUser>> getAllStaffStream() {
    return _db
        .collection('users')
        .where('role', whereIn: [
          UserRole.staff.name,
          UserRole.manager.name,
          UserRole.admin.name,
        ])
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => AppUser.fromMap(doc.data()))
          .toList();
    });
  }

  Future<void> updateStaffRole(String userId, UserRole newRole) async {
    try {
      await _db.collection('users').doc(userId).update({
        'role': newRole.name,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      // Log activity
      await _logSettingsActivity(
        action: 'update_staff_role',
        details: {
          'userId': userId,
          'newRole': newRole.name,
        },
      );
    } catch (e) {
      throw Exception('Failed to update staff role: $e');
    }
  }

  Future<void> deactivateStaff(String userId) async {
    try {
      await _db.collection('users').doc(userId).update({
        'isActive': false,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      // Log activity
      await _logSettingsActivity(
        action: 'deactivate_staff',
        details: {
          'userId': userId,
        },
      );
    } catch (e) {
      throw Exception('Failed to deactivate staff: $e');
    }
  }

  // System utilities
  Future<Map<String, dynamic>> getSystemInfo() async {
    try {
      // Get database stats
      final ordersSnapshot = await _db.collection('orders').get();
      final menuItemsSnapshot = await _db.collection('menu_items').get();
      final usersSnapshot = await _db.collection('users').get();

      return {
        'totalOrders': ordersSnapshot.docs.length,
        'totalMenuItems': menuItemsSnapshot.docs.length,
        'totalUsers': usersSnapshot.docs.length,
        'databaseSize': _calculateDatabaseSize([
          ordersSnapshot,
          menuItemsSnapshot,
          usersSnapshot,
        ]),
        'lastBackup': null, // TODO: Implement backup tracking
        'appVersion': '1.0.0',
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw Exception('Failed to get system info: $e');
    }
  }

  Future<void> exportData(String format) async {
    try {
      // TODO: Implement data export functionality
      await _logSettingsActivity(
        action: 'export_data',
        details: {
          'format': format,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      throw Exception('Failed to export data: $e');
    }
  }

  Future<void> backupData() async {
    try {
      // TODO: Implement backup functionality
      await _logSettingsActivity(
        action: 'backup_data',
        details: {
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      throw Exception('Failed to backup data: $e');
    }
  }

  // Helper methods
  RestaurantSettings _createDefaultSettings() {
    return RestaurantSettings(
      id: _defaultSettingsId,
      name: 'My Restaurant',
      description: 'A great place to dine',
      email: '<EMAIL>',
      phone: '+255 123 456 789',
      address: '123 Main Street',
      city: 'Dar es Salaam',
      country: 'Tanzania',
      timezone: 'Africa/Dar_es_Salaam',
      currency: 'TZS',
      operatingHours: {
        'monday': OperatingHours(),
        'tuesday': OperatingHours(),
        'wednesday': OperatingHours(),
        'thursday': OperatingHours(),
        'friday': OperatingHours(),
        'saturday': OperatingHours(),
        'sunday': OperatingHours(isOpen: false),
      },
      paymentSettings: PaymentSettings(),
      notificationSettings: NotificationSettings(),
      appSettings: AppSettings(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  double _calculateDatabaseSize(List<QuerySnapshot> snapshots) {
    // Rough estimation of database size in MB
    int totalDocs = 0;
    for (final snapshot in snapshots) {
      totalDocs += snapshot.docs.length;
    }
    return (totalDocs * 2.5) / 1024; // Rough estimate: 2.5KB per document
  }

  Future<void> _logSettingsActivity({
    required String action,
    required Map<String, dynamic> details,
  }) async {
    try {
      await _db.collection('settings_activities').add({
        'action': action,
        'details': details,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      // Log activity failure shouldn't break the main operation
      print('Failed to log settings activity: $e');
    }
  }

  // Get settings activities
  Stream<List<Map<String, dynamic>>> getSettingsActivitiesStream({
    int limit = 50,
  }) {
    return _db
        .collection('settings_activities')
        .orderBy('timestamp', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => {
                'id': doc.id,
                ...doc.data(),
              })
          .toList();
    });
  }
}

// Riverpod providers
final settingsServiceProvider = Provider<SettingsService>((ref) {
  return SettingsService();
});

// Restaurant settings stream provider
final restaurantSettingsStreamProvider = StreamProvider<RestaurantSettings>((ref) {
  return ref.read(settingsServiceProvider).getRestaurantSettingsStream();
});

// Staff list stream provider
final staffListStreamProvider = StreamProvider<List<AppUser>>((ref) {
  return ref.read(settingsServiceProvider).getAllStaffStream();
});

// System info provider
final systemInfoProvider = FutureProvider<Map<String, dynamic>>((ref) {
  return ref.read(settingsServiceProvider).getSystemInfo();
});

// Settings activities stream provider
final settingsActivitiesStreamProvider = StreamProvider<List<Map<String, dynamic>>>((ref) {
  return ref.read(settingsServiceProvider).getSettingsActivitiesStream();
});
