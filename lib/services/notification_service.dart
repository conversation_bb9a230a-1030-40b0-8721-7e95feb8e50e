import 'dart:convert';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../routes/routes.dart';
import 'crashlytics_service.dart';
import 'secure_storage_service.dart';

// Notification types
enum NotificationType {
  orderUpdate,
  promotion,
  general,
  payment,
}

// Notification data model
class AppNotification {
  final String id;
  final String title;
  final String body;
  final NotificationType type;
  final Map<String, dynamic>? data;
  final DateTime timestamp;
  final bool isRead;

  AppNotification({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    this.data,
    required this.timestamp,
    this.isRead = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type.name,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      'isRead': isRead,
    };
  }

  factory AppNotification.fromMap(Map<String, dynamic> map) {
    return AppNotification(
      id: map['id'],
      title: map['title'],
      body: map['body'],
      type: NotificationType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => NotificationType.general,
      ),
      data: map['data'],
      timestamp: DateTime.parse(map['timestamp']),
      isRead: map['isRead'] ?? false,
    );
  }
}

class NotificationService {
  final Ref _ref;
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  
  NotificationService(this._ref);

  // Initialize notifications
  Future<void> initialize() async {
    try {
      // Request permission
      final settings = await _messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      await _ref.read(crashlyticsServiceProvider).recordUserAction(
        'notification_permission_requested',
        parameters: {'status': settings.authorizationStatus.name},
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        // Get FCM token
        final token = await _messaging.getToken();
        if (token != null) {
          await _saveTokenSecurely(token);
          await _ref.read(crashlyticsServiceProvider).recordUserAction(
            'fcm_token_obtained',
          );
        }

        // Setup message handlers
        _setupMessageHandlers();
        
        // Setup token refresh listener
        _messaging.onTokenRefresh.listen((token) async {
          await _saveTokenSecurely(token);
        });
      }
    } catch (e) {
      await _ref.read(crashlyticsServiceProvider).recordError(
        e,
        StackTrace.current,
        reason: 'Failed to initialize notifications',
      );
    }
  }

  // Setup message handlers
  void _setupMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      _handleForegroundMessage(message);
    });

    // Handle background message taps
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      _handleMessageTap(message);
    });

    // Handle app launch from notification
    _messaging.getInitialMessage().then((RemoteMessage? message) {
      if (message != null) {
        _handleMessageTap(message);
      }
    });
  }

  // Handle foreground messages
  void _handleForegroundMessage(RemoteMessage message) async {
    try {
      await _ref.read(crashlyticsServiceProvider).recordUserAction(
        'notification_received_foreground',
        parameters: {
          'title': message.notification?.title ?? '',
          'type': message.data['type'] ?? 'unknown',
        },
      );

      // Store notification locally
      await _storeNotificationLocally(message);

      // Show in-app notification if needed
      _showInAppNotification(message);
    } catch (e) {
      await _ref.read(crashlyticsServiceProvider).recordError(
        e,
        StackTrace.current,
        reason: 'Failed to handle foreground message',
      );
    }
  }

  // Handle notification tap
  void _handleMessageTap(RemoteMessage message) async {
    try {
      await _ref.read(crashlyticsServiceProvider).recordUserAction(
        'notification_tapped',
        parameters: {
          'title': message.notification?.title ?? '',
          'type': message.data['type'] ?? 'unknown',
        },
      );

      // Navigate based on notification type
      final notificationType = message.data['type'];
      final context = _getNavigationContext();
      
      if (context != null) {
        switch (notificationType) {
          case 'order_update':
            final orderId = message.data['orderId'];
            if (orderId != null) {
              context.goNamed(AppRoute.orders.name);
            }
            break;
          case 'promotion':
            context.goNamed(AppRoute.menu.name);
            break;
          case 'payment':
            context.goNamed(AppRoute.profile.name);
            break;
          default:
            // Default navigation
            break;
        }
      }
    } catch (e) {
      await _ref.read(crashlyticsServiceProvider).recordError(
        e,
        StackTrace.current,
        reason: 'Failed to handle message tap',
      );
    }
  }

  // Show in-app notification
  void _showInAppNotification(RemoteMessage message) {
    final context = _getNavigationContext();
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                message.notification?.title ?? 'Notification',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              if (message.notification?.body != null)
                Text(message.notification!.body!),
            ],
          ),
          action: SnackBarAction(
            label: 'View',
            onPressed: () => _handleMessageTap(message),
          ),
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  // Store notification locally
  Future<void> _storeNotificationLocally(RemoteMessage message) async {
    try {
      final notification = AppNotification(
        id: message.messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
        title: message.notification?.title ?? 'Notification',
        body: message.notification?.body ?? '',
        type: _parseNotificationType(message.data['type']),
        data: message.data,
        timestamp: DateTime.now(),
      );

      final secureStorage = _ref.read(secureStorageServiceProvider);
      final existingNotifications = await _getStoredNotifications();
      existingNotifications.insert(0, notification);

      // Keep only last 50 notifications
      if (existingNotifications.length > 50) {
        existingNotifications.removeRange(50, existingNotifications.length);
      }

      await secureStorage.saveSecureData(
        'notifications',
        jsonEncode(existingNotifications.map((n) => n.toMap()).toList()),
      );
    } catch (e) {
      await _ref.read(crashlyticsServiceProvider).recordError(
        e,
        StackTrace.current,
        reason: 'Failed to store notification locally',
      );
    }
  }

  // Get stored notifications
  Future<List<AppNotification>> _getStoredNotifications() async {
    try {
      final secureStorage = _ref.read(secureStorageServiceProvider);
      final notificationsJson = await secureStorage.getSecureData('notifications');
      
      if (notificationsJson != null) {
        final List<dynamic> notificationsList = jsonDecode(notificationsJson);
        return notificationsList.map((n) => AppNotification.fromMap(n)).toList();
      }
    } catch (e) {
      await _ref.read(crashlyticsServiceProvider).recordError(
        e,
        StackTrace.current,
        reason: 'Failed to get stored notifications',
      );
    }
    return [];
  }

  // Get FCM token
  Future<String?> getToken() async {
    try {
      return await _messaging.getToken();
    } catch (e) {
      await _ref.read(crashlyticsServiceProvider).recordError(
        e,
        StackTrace.current,
        reason: 'Failed to get FCM token',
      );
      return null;
    }
  }

  // Save token securely
  Future<void> _saveTokenSecurely(String token) async {
    try {
      final secureStorage = _ref.read(secureStorageServiceProvider);
      await secureStorage.saveSecureData('fcm_token', token);
    } catch (e) {
      await _ref.read(crashlyticsServiceProvider).recordError(
        e,
        StackTrace.current,
        reason: 'Failed to save FCM token',
      );
    }
  }

  // Get stored token
  Future<String?> getStoredToken() async {
    try {
      final secureStorage = _ref.read(secureStorageServiceProvider);
      return await secureStorage.getSecureData('fcm_token');
    } catch (e) {
      return null;
    }
  }

  // Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _messaging.subscribeToTopic(topic);
      await _ref.read(crashlyticsServiceProvider).recordUserAction(
        'subscribed_to_topic',
        parameters: {'topic': topic},
      );
    } catch (e) {
      await _ref.read(crashlyticsServiceProvider).recordError(
        e,
        StackTrace.current,
        reason: 'Failed to subscribe to topic: $topic',
      );
    }
  }

  // Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _messaging.unsubscribeFromTopic(topic);
      await _ref.read(crashlyticsServiceProvider).recordUserAction(
        'unsubscribed_from_topic',
        parameters: {'topic': topic},
      );
    } catch (e) {
      await _ref.read(crashlyticsServiceProvider).recordError(
        e,
        StackTrace.current,
        reason: 'Failed to unsubscribe from topic: $topic',
      );
    }
  }

  // Get notification settings
  Future<Map<String, bool>> getNotificationSettings() async {
    final secureStorage = _ref.read(secureStorageServiceProvider);
    final settings = await secureStorage.getNotificationSettings();
    
    return settings ?? {
      'orderUpdates': true,
      'promotions': true,
      'general': true,
      'payment': true,
    };
  }

  // Update notification settings
  Future<void> updateNotificationSettings(Map<String, bool> settings) async {
    try {
      final secureStorage = _ref.read(secureStorageServiceProvider);
      await secureStorage.saveNotificationSettings(settings);

      // Subscribe/unsubscribe from topics based on settings
      if (settings['promotions'] == true) {
        await subscribeToTopic('promotions');
      } else {
        await unsubscribeFromTopic('promotions');
      }

      if (settings['general'] == true) {
        await subscribeToTopic('general');
      } else {
        await unsubscribeFromTopic('general');
      }
    } catch (e) {
      await _ref.read(crashlyticsServiceProvider).recordError(
        e,
        StackTrace.current,
        reason: 'Failed to update notification settings',
      );
    }
  }

  // Helper methods
  NotificationType _parseNotificationType(String? type) {
    switch (type) {
      case 'order_update':
        return NotificationType.orderUpdate;
      case 'promotion':
        return NotificationType.promotion;
      case 'payment':
        return NotificationType.payment;
      default:
        return NotificationType.general;
    }
  }

  BuildContext? _getNavigationContext() {
    // This would need to be implemented based on your navigation setup
    // You might need to store a reference to the navigator key
    return null;
  }

  // Clear all notifications
  Future<void> clearAllNotifications() async {
    try {
      final secureStorage = _ref.read(secureStorageServiceProvider);
      await secureStorage.deleteSecureData('notifications');
    } catch (e) {
      await _ref.read(crashlyticsServiceProvider).recordError(
        e,
        StackTrace.current,
        reason: 'Failed to clear notifications',
      );
    }
  }

  // Mark notification as read
  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      final notifications = await _getStoredNotifications();
      final index = notifications.indexWhere((n) => n.id == notificationId);
      
      if (index != -1) {
        notifications[index] = AppNotification(
          id: notifications[index].id,
          title: notifications[index].title,
          body: notifications[index].body,
          type: notifications[index].type,
          data: notifications[index].data,
          timestamp: notifications[index].timestamp,
          isRead: true,
        );

        final secureStorage = _ref.read(secureStorageServiceProvider);
        await secureStorage.saveSecureData(
          'notifications',
          jsonEncode(notifications.map((n) => n.toMap()).toList()),
        );
      }
    } catch (e) {
      await _ref.read(crashlyticsServiceProvider).recordError(
        e,
        StackTrace.current,
        reason: 'Failed to mark notification as read',
      );
    }
  }

  // Get unread notification count
  Future<int> getUnreadNotificationCount() async {
    try {
      final notifications = await _getStoredNotifications();
      return notifications.where((n) => !n.isRead).length;
    } catch (e) {
      return 0;
    }
  }
}

// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Handle background messages here
  print('Handling a background message: ${message.messageId}');
}

// Riverpod provider for NotificationService
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService(ref);
});
