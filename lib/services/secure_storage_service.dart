import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SecureStorageService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Storage keys
  static const String _userTokenKey = 'user_token';
  static const String _userIdKey = 'user_id';
  static const String _userEmailKey = 'user_email';
  static const String _userPreferencesKey = 'user_preferences';
  static const String _cartDataKey = 'cart_data';
  static const String _biometricEnabledKey = 'biometric_enabled';
  static const String _notificationSettingsKey = 'notification_settings';
  static const String _themePreferenceKey = 'theme_preference';
  static const String _languagePreferenceKey = 'language_preference';
  static const String _lastLoginTimeKey = 'last_login_time';
  static const String _paymentMethodsKey = 'payment_methods';
  static const String _deliveryAddressesKey = 'delivery_addresses';

  // User authentication data
  Future<void> saveUserToken(String token) async {
    await _storage.write(key: _userTokenKey, value: token);
  }

  Future<String?> getUserToken() async {
    return await _storage.read(key: _userTokenKey);
  }

  Future<void> saveUserId(String userId) async {
    await _storage.write(key: _userIdKey, value: userId);
  }

  Future<String?> getUserId() async {
    return await _storage.read(key: _userIdKey);
  }

  Future<void> saveUserEmail(String email) async {
    await _storage.write(key: _userEmailKey, value: email);
  }

  Future<String?> getUserEmail() async {
    return await _storage.read(key: _userEmailKey);
  }

  // User preferences
  Future<void> saveUserPreferences(Map<String, dynamic> preferences) async {
    final preferencesJson = jsonEncode(preferences);
    await _storage.write(key: _userPreferencesKey, value: preferencesJson);
  }

  Future<Map<String, dynamic>?> getUserPreferences() async {
    final preferencesJson = await _storage.read(key: _userPreferencesKey);
    if (preferencesJson != null) {
      return jsonDecode(preferencesJson) as Map<String, dynamic>;
    }
    return null;
  }

  // Cart data persistence
  Future<void> saveCartData(Map<String, dynamic> cartData) async {
    final cartJson = jsonEncode(cartData);
    await _storage.write(key: _cartDataKey, value: cartJson);
  }

  Future<Map<String, dynamic>?> getCartData() async {
    final cartJson = await _storage.read(key: _cartDataKey);
    if (cartJson != null) {
      return jsonDecode(cartJson) as Map<String, dynamic>;
    }
    return null;
  }

  Future<void> clearCartData() async {
    await _storage.delete(key: _cartDataKey);
  }

  // Biometric authentication settings
  Future<void> setBiometricEnabled(bool enabled) async {
    await _storage.write(key: _biometricEnabledKey, value: enabled.toString());
  }

  Future<bool> isBiometricEnabled() async {
    final value = await _storage.read(key: _biometricEnabledKey);
    return value == 'true';
  }

  // Notification settings
  Future<void> saveNotificationSettings(Map<String, bool> settings) async {
    final settingsJson = jsonEncode(settings);
    await _storage.write(key: _notificationSettingsKey, value: settingsJson);
  }

  Future<Map<String, bool>?> getNotificationSettings() async {
    final settingsJson = await _storage.read(key: _notificationSettingsKey);
    if (settingsJson != null) {
      final decoded = jsonDecode(settingsJson) as Map<String, dynamic>;
      return decoded.map((key, value) => MapEntry(key, value as bool));
    }
    return null;
  }

  // Theme preference
  Future<void> saveThemePreference(String theme) async {
    await _storage.write(key: _themePreferenceKey, value: theme);
  }

  Future<String?> getThemePreference() async {
    return await _storage.read(key: _themePreferenceKey);
  }

  // Language preference
  Future<void> saveLanguagePreference(String language) async {
    await _storage.write(key: _languagePreferenceKey, value: language);
  }

  Future<String?> getLanguagePreference() async {
    return await _storage.read(key: _languagePreferenceKey);
  }

  // Last login time
  Future<void> saveLastLoginTime(DateTime dateTime) async {
    await _storage.write(key: _lastLoginTimeKey, value: dateTime.toIso8601String());
  }

  Future<DateTime?> getLastLoginTime() async {
    final dateTimeString = await _storage.read(key: _lastLoginTimeKey);
    if (dateTimeString != null) {
      return DateTime.parse(dateTimeString);
    }
    return null;
  }

  // Payment methods (tokenized data only)
  Future<void> savePaymentMethods(List<Map<String, dynamic>> paymentMethods) async {
    final paymentMethodsJson = jsonEncode(paymentMethods);
    await _storage.write(key: _paymentMethodsKey, value: paymentMethodsJson);
  }

  Future<List<Map<String, dynamic>>?> getPaymentMethods() async {
    final paymentMethodsJson = await _storage.read(key: _paymentMethodsKey);
    if (paymentMethodsJson != null) {
      final decoded = jsonDecode(paymentMethodsJson) as List<dynamic>;
      return decoded.cast<Map<String, dynamic>>();
    }
    return null;
  }

  // Delivery addresses
  Future<void> saveDeliveryAddresses(List<Map<String, dynamic>> addresses) async {
    final addressesJson = jsonEncode(addresses);
    await _storage.write(key: _deliveryAddressesKey, value: addressesJson);
  }

  Future<List<Map<String, dynamic>>?> getDeliveryAddresses() async {
    final addressesJson = await _storage.read(key: _deliveryAddressesKey);
    if (addressesJson != null) {
      final decoded = jsonDecode(addressesJson) as List<dynamic>;
      return decoded.cast<Map<String, dynamic>>();
    }
    return null;
  }

  // Generic secure storage methods
  Future<void> saveSecureData(String key, String value) async {
    await _storage.write(key: key, value: value);
  }

  Future<String?> getSecureData(String key) async {
    return await _storage.read(key: key);
  }

  Future<void> deleteSecureData(String key) async {
    await _storage.delete(key: key);
  }

  // Clear all user data (for logout)
  Future<void> clearUserData() async {
    await _storage.delete(key: _userTokenKey);
    await _storage.delete(key: _userIdKey);
    await _storage.delete(key: _userEmailKey);
    await _storage.delete(key: _userPreferencesKey);
    await _storage.delete(key: _cartDataKey);
    await _storage.delete(key: _lastLoginTimeKey);
    await _storage.delete(key: _paymentMethodsKey);
    await _storage.delete(key: _deliveryAddressesKey);
  }

  // Clear all app data (for app reset)
  Future<void> clearAllData() async {
    await _storage.deleteAll();
  }

  // Check if user data exists (for auto-login)
  Future<bool> hasUserData() async {
    final token = await getUserToken();
    final userId = await getUserId();
    return token != null && userId != null;
  }

  // Get all stored keys (for debugging)
  Future<Map<String, String>> getAllData() async {
    return await _storage.readAll();
  }

  // Check if storage is available
  Future<bool> isStorageAvailable() async {
    try {
      await _storage.write(key: 'test_key', value: 'test_value');
      final value = await _storage.read(key: 'test_key');
      await _storage.delete(key: 'test_key');
      return value == 'test_value';
    } catch (e) {
      return false;
    }
  }

  // Backup user data to JSON
  Future<Map<String, dynamic>> backupUserData() async {
    final userData = <String, dynamic>{};
    
    final token = await getUserToken();
    if (token != null) userData['token'] = token;
    
    final userId = await getUserId();
    if (userId != null) userData['userId'] = userId;
    
    final email = await getUserEmail();
    if (email != null) userData['email'] = email;
    
    final preferences = await getUserPreferences();
    if (preferences != null) userData['preferences'] = preferences;
    
    final notificationSettings = await getNotificationSettings();
    if (notificationSettings != null) userData['notificationSettings'] = notificationSettings;
    
    final themePreference = await getThemePreference();
    if (themePreference != null) userData['themePreference'] = themePreference;
    
    final languagePreference = await getLanguagePreference();
    if (languagePreference != null) userData['languagePreference'] = languagePreference;
    
    return userData;
  }

  // Restore user data from JSON
  Future<void> restoreUserData(Map<String, dynamic> userData) async {
    if (userData.containsKey('token')) {
      await saveUserToken(userData['token']);
    }
    
    if (userData.containsKey('userId')) {
      await saveUserId(userData['userId']);
    }
    
    if (userData.containsKey('email')) {
      await saveUserEmail(userData['email']);
    }
    
    if (userData.containsKey('preferences')) {
      await saveUserPreferences(userData['preferences']);
    }
    
    if (userData.containsKey('notificationSettings')) {
      await saveNotificationSettings(
        Map<String, bool>.from(userData['notificationSettings'])
      );
    }
    
    if (userData.containsKey('themePreference')) {
      await saveThemePreference(userData['themePreference']);
    }
    
    if (userData.containsKey('languagePreference')) {
      await saveLanguagePreference(userData['languagePreference']);
    }
  }
}

// Riverpod provider for SecureStorageService
final secureStorageServiceProvider = Provider<SecureStorageService>((ref) {
  return SecureStorageService();
});
