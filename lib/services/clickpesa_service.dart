// File: lib/services/clickpesa_service.dart
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:smart_restaurant_menu/config/clickpesa_config.dart';

/// ClickPesa payment service for Tanzania mobile money and card payments
class ClickPesaService {
  static String get _baseUrl => ClickPesaConfig.apiBaseUrl;

  // Get credentials from configuration
  static String get _apiKey => ClickPesaConfig.apiKey;
  static String get _clientId => ClickPesaConfig.clientId;

  final http.Client _httpClient;

  ClickPesaService({http.Client? httpClient})
    : _httpClient = httpClient ?? http.Client();

  /// Generate authentication token for ClickPesa API
  Future<String?> _generateToken() async {
    try {
      final response = await _httpClient.post(
        Uri.parse('$_baseUrl/third-parties/generate-token'),
        headers: {'api-key': _apiKey, 'client-id': _clientId},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['token'];
      } else {
        debugPrint('Failed to generate ClickPesa token: ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Error generating ClickPesa token: $e');
      return null;
    }
  }

  /// Create a mobile money payment request with ClickPesa
  Future<ClickPesaPaymentResult> createPayment({
    required double amount,
    required String customerPhone,
    required String customerEmail,
    required String customerName,
    required ClickPesaPaymentMethod paymentMethod,
    String? orderId,
    String? description,
    String? callbackUrl,
  }) async {
    try {
      final token = await _generateToken();
      if (token == null) {
        return ClickPesaPaymentResult.error(
          'Failed to authenticate with ClickPesa',
        );
      }

      final orderReference =
          orderId ?? DateTime.now().millisecondsSinceEpoch.toString();
      final formattedPhone = formatPhoneNumber(customerPhone);

      // Debug logging for phone number formatting
      debugPrint('Original phone: $customerPhone');
      debugPrint('Formatted phone: $formattedPhone');

      final paymentData = {
        'amount': amount.toString(),
        'currency': 'TZS',
        'orderReference': orderReference,
        'phoneNumber': formattedPhone,
        // Generate checksum according to ClickPesa documentation
        'checksum': _generateChecksum(amount, orderReference, formattedPhone),
      };

      debugPrint('Payment data: $paymentData');

      final response = await _httpClient.post(
        Uri.parse(
          '$_baseUrl/third-parties/payments/initiate-ussd-push-request',
        ),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token, // Token already includes 'Bearer' prefix
        },
        body: jsonEncode(paymentData),
      );

      final responseData = jsonDecode(response.body);

      // Debug logging to understand the response structure
      debugPrint('ClickPesa Response Status: ${response.statusCode}');
      debugPrint('ClickPesa Response Body: ${response.body}');

      if (response.statusCode == 200) {
        // Safely extract fields with type checking
        final id = responseData['id'];
        final status = responseData['status'];
        final orderRef = responseData['orderReference'];

        debugPrint(
          'Response fields - id: $id (${id.runtimeType}), status: $status (${status.runtimeType}), orderRef: $orderRef (${orderRef.runtimeType})',
        );

        return ClickPesaPaymentResult.success(
          transactionId: id?.toString() ?? 'unknown',
          paymentUrl: null, // USSD push doesn't return a URL
          status: status?.toString() ?? 'unknown',
          message: 'Payment request sent to $customerPhone',
          orderReference: orderRef?.toString() ?? orderReference,
        );
      } else {
        final errorMessage = responseData['message'];
        return ClickPesaPaymentResult.error(
          errorMessage?.toString() ?? 'Payment creation failed',
        );
      }
    } catch (e) {
      debugPrint('Error creating ClickPesa payment: $e');
      return ClickPesaPaymentResult.error('Network error: $e');
    }
  }

  /// Generate checksum for payment request according to ClickPesa documentation
  ///
  /// Steps:
  /// 1. Sort payload keys alphabetically
  /// 2. Concatenate sorted values into a single string
  /// 3. Generate HMAC-SHA256 hash with checksum key
  /// 4. Return hex digest
  String _generateChecksum(
    double amount,
    String orderReference,
    String phoneNumber,
  ) {
    // Create payload map (excluding checksum field)
    final payload = {
      'amount': amount.toString(),
      'currency': 'TZS',
      'orderReference': orderReference,
      'phoneNumber': phoneNumber,
    };

    // Sort keys alphabetically and concatenate values
    final sortedKeys = payload.keys.toList()..sort();
    final concatenatedValues = sortedKeys.map((key) => payload[key]).join('');

    // Generate HMAC-SHA256 hash using the checksum key
    final checksumKey = ClickPesaConfig.checksumKey;
    final key = utf8.encode(checksumKey);
    final bytes = utf8.encode(concatenatedValues);
    final hmac = Hmac(sha256, key);
    final digest = hmac.convert(bytes);

    return digest.toString();
  }

  /// Check payment status using order reference
  Future<ClickPesaPaymentStatus> checkPaymentStatus(
    String orderReference,
  ) async {
    try {
      final token = await _generateToken();
      if (token == null) {
        return ClickPesaPaymentStatus.error('Authentication failed');
      }

      final response = await _httpClient.get(
        Uri.parse('$_baseUrl/third-parties/payments/$orderReference'),
        headers: {
          'Authorization': token, // Token already includes 'Bearer' prefix
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        // API returns an array, take the first item
        final paymentData = responseData[0];

        return ClickPesaPaymentStatus(
          transactionId: paymentData['id'],
          status: paymentData['status'],
          amount:
              double.tryParse(paymentData['collectedAmount'].toString()) ?? 0.0,
          currency: paymentData['collectedCurrency'],
          customerPhone: paymentData['customer']['customerPhoneNumber'],
          paymentMethod: null, // Not provided in response
          orderReference: paymentData['orderReference'],
          paymentReference: paymentData['paymentReference'],
          createdAt: DateTime.tryParse(paymentData['createdAt'] ?? ''),
          updatedAt: DateTime.tryParse(paymentData['updatedAt'] ?? ''),
        );
      } else {
        final responseData = jsonDecode(response.body);
        return ClickPesaPaymentStatus.error(
          responseData['message'] ?? 'Status check failed',
        );
      }
    } catch (e) {
      debugPrint('Error checking ClickPesa payment status: $e');
      return ClickPesaPaymentStatus.error('Network error: $e');
    }
  }

  /// Get supported mobile money providers
  List<MobileMoney> getSupportedMobileMoney() {
    return [
      MobileMoney.mpesa,
      MobileMoney.tigoPesa,
      MobileMoney.airtelMoney,
      MobileMoney.haloPesa,
      MobileMoney.tPesa,
    ];
  }

  /// Validate phone number for mobile money
  bool isValidMobileMoneyPhone(String phone) {
    return ClickPesaConfig.isValidTanzanianPhone(phone);
  }

  /// Format phone number for ClickPesa API
  String formatPhoneNumber(String phone) {
    return ClickPesaConfig.formatPhoneNumber(phone);
  }

  void dispose() {
    _httpClient.close();
  }
}

/// Payment method enum for ClickPesa
enum ClickPesaPaymentMethod {
  mpesa('MPESA'),
  tigoPesa('TIGO_PESA'),
  airtelMoney('AIRTEL_MONEY'),
  haloPesa('HALO_PESA'),
  tPesa('T_PESA'),
  card('CARD'),
  bankTransfer('BANK_TRANSFER');

  const ClickPesaPaymentMethod(this.value);
  final String value;
}

/// Mobile money providers
enum MobileMoney {
  mpesa('M-Pesa', 'assets/images/mpesa_logo.png'),
  tigoPesa('Tigo Pesa', 'assets/images/tigo_logo.png'),
  airtelMoney('Airtel Money', 'assets/images/airtel_logo.png'),
  haloPesa('Halo Pesa', 'assets/images/halo_logo.png'),
  tPesa('T-Pesa', 'assets/images/tpesa_logo.png');

  const MobileMoney(this.displayName, this.logoPath);
  final String displayName;
  final String logoPath;
}

/// Payment result class
class ClickPesaPaymentResult {
  final bool isSuccess;
  final String? transactionId;
  final String? paymentUrl;
  final String? status;
  final String? orderReference;
  final String message;

  ClickPesaPaymentResult._({
    required this.isSuccess,
    this.transactionId,
    this.paymentUrl,
    this.status,
    this.orderReference,
    required this.message,
  });

  factory ClickPesaPaymentResult.success({
    String? transactionId,
    String? paymentUrl,
    String? status,
    String? orderReference,
    String? message,
  }) {
    return ClickPesaPaymentResult._(
      isSuccess: true,
      transactionId: transactionId,
      paymentUrl: paymentUrl,
      status: status,
      orderReference: orderReference,
      message: message ?? 'Payment created successfully',
    );
  }

  factory ClickPesaPaymentResult.error(String message) {
    return ClickPesaPaymentResult._(isSuccess: false, message: message);
  }
}

/// Payment status class
class ClickPesaPaymentStatus {
  final String? transactionId;
  final String? status;
  final double amount;
  final String? currency;
  final String? customerPhone;
  final String? paymentMethod;
  final String? orderReference;
  final String? paymentReference;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? errorMessage;

  ClickPesaPaymentStatus({
    this.transactionId,
    this.status,
    this.amount = 0.0,
    this.currency,
    this.customerPhone,
    this.paymentMethod,
    this.orderReference,
    this.paymentReference,
    this.createdAt,
    this.updatedAt,
    this.errorMessage,
  });

  factory ClickPesaPaymentStatus.error(String message) {
    return ClickPesaPaymentStatus(errorMessage: message);
  }

  bool get isSuccess => errorMessage == null && status == 'SUCCESS';
  bool get isPending => errorMessage == null && status == 'PROCESSING';
  bool get isFailed => errorMessage != null || status == 'FAILED';
}

/// Riverpod provider for ClickPesa service
final clickPesaServiceProvider = Provider<ClickPesaService>((ref) {
  return ClickPesaService();
});
