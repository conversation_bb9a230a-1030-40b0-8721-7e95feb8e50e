import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class CrashlyticsService {
  final FirebaseCrashlytics _crashlytics = FirebaseCrashlytics.instance;

  /// Log a custom error to Crashlytics
  Future<void> recordError(
    dynamic exception,
    StackTrace? stackTrace, {
    String? reason,
    bool fatal = false,
  }) async {
    await _crashlytics.recordError(
      exception,
      stackTrace,
      reason: reason,
      fatal: fatal,
    );
  }

  /// Log a custom message to Crashlytics
  Future<void> log(String message) async {
    await _crashlytics.log(message);
  }

  /// Set user identifier for crash reports
  Future<void> setUserIdentifier(String identifier) async {
    await _crashlytics.setUserIdentifier(identifier);
  }

  /// Set custom key-value pairs for crash reports
  Future<void> setCustomKey(String key, dynamic value) async {
    await _crashlytics.setCustomKey(key, value);
  }

  /// Record a Flutter error
  void recordFlutterError(FlutterErrorDetails errorDetails) {
    _crashlytics.recordFlutterFatalError(errorDetails);
  }

  /// Check if crash reporting is enabled
  bool get isCrashlyticsCollectionEnabled {
    return _crashlytics.isCrashlyticsCollectionEnabled;
  }

  /// Enable or disable crash reporting
  Future<void> setCrashlyticsCollectionEnabled(bool enabled) async {
    await _crashlytics.setCrashlyticsCollectionEnabled(enabled);
  }

  /// Send any unsent crash reports
  Future<void> sendUnsentReports() async {
    await _crashlytics.sendUnsentReports();
  }

  /// Delete any unsent crash reports
  Future<void> deleteUnsentReports() async {
    await _crashlytics.deleteUnsentReports();
  }

  /// Check if there are any unsent crash reports
  Future<bool> checkForUnsentReports() async {
    return await _crashlytics.checkForUnsentReports();
  }

  /// Record a breadcrumb for debugging
  Future<void> recordBreadcrumb(
    String message, {
    Map<String, String>? data,
  }) async {
    await log('BREADCRUMB: $message${data != null ? ' - Data: $data' : ''}');
  }

  /// Record user action for debugging
  Future<void> recordUserAction(
    String action, {
    Map<String, dynamic>? parameters,
  }) async {
    await log(
      'USER_ACTION: $action${parameters != null ? ' - Params: $parameters' : ''}',
    );

    if (parameters != null) {
      for (final entry in parameters.entries) {
        await setCustomKey('last_action_${entry.key}', entry.value);
      }
    }
    await setCustomKey('last_user_action', action);
  }

  /// Record app state for debugging
  Future<void> recordAppState(
    String state, {
    Map<String, dynamic>? context,
  }) async {
    await log(
      'APP_STATE: $state${context != null ? ' - Context: $context' : ''}',
    );
    await setCustomKey('app_state', state);

    if (context != null) {
      for (final entry in context.entries) {
        await setCustomKey('state_${entry.key}', entry.value);
      }
    }
  }

  /// Record authentication events
  Future<void> recordAuthEvent(
    String event, {
    String? userId,
    String? error,
  }) async {
    await log(
      'AUTH_EVENT: $event${userId != null ? ' - User: $userId' : ''}${error != null ? ' - Error: $error' : ''}',
    );
    await setCustomKey('last_auth_event', event);

    if (userId != null) {
      await setUserIdentifier(userId);
    }

    if (error != null) {
      await setCustomKey('last_auth_error', error);
    }
  }

  /// Record payment events
  Future<void> recordPaymentEvent(
    String event, {
    double? amount,
    String? currency,
    String? error,
  }) async {
    await log(
      'PAYMENT_EVENT: $event${amount != null ? ' - Amount: $amount $currency' : ''}${error != null ? ' - Error: $error' : ''}',
    );
    await setCustomKey('last_payment_event', event);

    if (amount != null) {
      await setCustomKey('last_payment_amount', amount);
    }

    if (currency != null) {
      await setCustomKey('last_payment_currency', currency);
    }

    if (error != null) {
      await setCustomKey('last_payment_error', error);
    }
  }

  /// Record order events
  Future<void> recordOrderEvent(
    String event, {
    String? orderId,
    double? total,
    String? error,
  }) async {
    await log(
      'ORDER_EVENT: $event${orderId != null ? ' - Order: $orderId' : ''}${total != null ? ' - Total: $total' : ''}${error != null ? ' - Error: $error' : ''}',
    );
    await setCustomKey('last_order_event', event);

    if (orderId != null) {
      await setCustomKey('last_order_id', orderId);
    }

    if (total != null) {
      await setCustomKey('last_order_total', total);
    }

    if (error != null) {
      await setCustomKey('last_order_error', error);
    }
  }

  /// Test crash reporting (for debugging only)
  void testCrash() {
    _crashlytics.crash();
  }
}

// Riverpod provider for CrashlyticsService
final crashlyticsServiceProvider = Provider<CrashlyticsService>((ref) {
  return CrashlyticsService();
});
