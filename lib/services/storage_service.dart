// File: lib/services/storage_service.dart
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:smart_restaurant_menu/services/supabase_storage_service.dart';

/// Storage service that wraps Supabase storage functionality
/// This provides a consistent interface for storage operations
class StorageService {
  /// Upload profile image for a user
  Future<String?> uploadProfileImage(String userId, File imageFile) async {
    try {
      return await SupabaseStorageService.uploadProfileImage(userId, imageFile);
    } catch (e) {
      debugPrint('StorageService: Error uploading profile image: $e');
      return null;
    }
  }

  /// Upload menu item image
  Future<String?> uploadMenuItemImage(File imageFile, {String? menuItemId}) async {
    try {
      return await SupabaseStorageService.uploadMenuItemImage(imageFile, menuItemId: menuItemId);
    } catch (e) {
      debugPrint('StorageService: Error uploading menu item image: $e');
      return null;
    }
  }

  /// Upload a general image file
  Future<String?> uploadImage({
    required File file,
    String? customPath,
    String? bucket,
  }) async {
    try {
      return await SupabaseStorageService.uploadImage(
        file: file,
        customPath: customPath,
        bucket: bucket ?? 'images',
      );
    } catch (e) {
      debugPrint('StorageService: Error uploading image: $e');
      return null;
    }
  }

  /// Upload image from bytes (useful for web)
  Future<String?> uploadImageFromBytes({
    required Uint8List bytes,
    required String fileName,
    String? bucket,
  }) async {
    try {
      return await SupabaseStorageService.uploadImageFromBytes(
        bytes: bytes,
        fileName: fileName,
        bucket: bucket ?? 'images',
      );
    } catch (e) {
      debugPrint('StorageService: Error uploading image from bytes: $e');
      return null;
    }
  }

  /// Delete a file from storage
  Future<bool> deleteFile({
    required String filePath,
    String? bucket,
  }) async {
    try {
      return await SupabaseStorageService.deleteFile(
        filePath: filePath,
        bucket: bucket ?? 'images',
      );
    } catch (e) {
      debugPrint('StorageService: Error deleting file: $e');
      return false;
    }
  }

  /// Get signed URL for private files
  Future<String?> getSignedUrl({
    required String filePath,
    String? bucket,
    int expiresIn = 3600,
  }) async {
    try {
      return await SupabaseStorageService.getSignedUrl(
        filePath: filePath,
        bucket: bucket ?? 'images',
        expiresIn: expiresIn,
      );
    } catch (e) {
      debugPrint('StorageService: Error getting signed URL: $e');
      return null;
    }
  }

  /// Initialize storage buckets
  Future<void> initializeBuckets() async {
    try {
      await SupabaseStorageService.initializeBuckets();
    } catch (e) {
      debugPrint('StorageService: Error initializing buckets: $e');
    }
  }
}

/// Riverpod provider for StorageService
final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});
