import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';

class ImageCompressionService {
  final Uuid _uuid = const Uuid();

  /// Compress an image file
  /// Returns a new compressed file
  Future<File> compressImage({
    required File imageFile,
    int maxWidth = 1024,
    int maxHeight = 1024,
    int quality = 85,
    String? outputPath,
  }) async {
    try {
      // For now, we'll implement a basic compression
      // In a production app, you might want to use packages like:
      // - flutter_image_compress
      // - image (dart package)
      
      // Generate output path if not provided
      final String finalOutputPath = outputPath ?? 
          '${path.dirname(imageFile.path)}/compressed_${_uuid.v4()}${path.extension(imageFile.path)}';

      // For this POC, we'll copy the file and return it
      // In production, implement actual compression here
      final File compressedFile = await imageFile.copy(finalOutputPath);
      
      return compressedFile;
    } catch (e) {
      throw ImageCompressionException('Failed to compress image: $e');
    }
  }

  /// Compress image to specific file size (approximate)
  Future<File> compressToSize({
    required File imageFile,
    int targetSizeInKB = 500,
    String? outputPath,
  }) async {
    try {
      // Start with high quality and reduce if needed
      int quality = 90;
      File compressedFile = imageFile;
      
      while (quality > 10) {
        compressedFile = await compressImage(
          imageFile: imageFile,
          quality: quality,
          outputPath: outputPath,
        );
        
        final int fileSizeInKB = compressedFile.lengthSync() ~/ 1024;
        
        if (fileSizeInKB <= targetSizeInKB) {
          break;
        }
        
        quality -= 10;
      }
      
      return compressedFile;
    } catch (e) {
      throw ImageCompressionException('Failed to compress image to target size: $e');
    }
  }

  /// Get optimized dimensions while maintaining aspect ratio
  Map<String, int> getOptimizedDimensions({
    required int originalWidth,
    required int originalHeight,
    int maxWidth = 1024,
    int maxHeight = 1024,
  }) {
    if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
      return {'width': originalWidth, 'height': originalHeight};
    }

    final double aspectRatio = originalWidth / originalHeight;
    
    int newWidth = maxWidth;
    int newHeight = (newWidth / aspectRatio).round();
    
    if (newHeight > maxHeight) {
      newHeight = maxHeight;
      newWidth = (newHeight * aspectRatio).round();
    }
    
    return {'width': newWidth, 'height': newHeight};
  }

  /// Check if image needs compression
  bool needsCompression({
    required File imageFile,
    int maxSizeInMB = 2,
    int maxWidth = 1024,
    int maxHeight = 1024,
  }) {
    final int fileSizeInBytes = imageFile.lengthSync();
    final int maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    
    // Check file size
    if (fileSizeInBytes > maxSizeInBytes) {
      return true;
    }
    
    // For dimension check, you'd need to decode the image
    // This is a simplified check based on file size only
    return false;
  }

  /// Create thumbnail from image
  Future<File> createThumbnail({
    required File imageFile,
    int thumbnailSize = 200,
    String? outputPath,
  }) async {
    try {
      final String finalOutputPath = outputPath ?? 
          '${path.dirname(imageFile.path)}/thumb_${_uuid.v4()}${path.extension(imageFile.path)}';

      // For this POC, we'll copy the file
      // In production, implement actual thumbnail creation
      final File thumbnailFile = await compressImage(
        imageFile: imageFile,
        maxWidth: thumbnailSize,
        maxHeight: thumbnailSize,
        quality: 80,
        outputPath: finalOutputPath,
      );
      
      return thumbnailFile;
    } catch (e) {
      throw ImageCompressionException('Failed to create thumbnail: $e');
    }
  }

  /// Batch compress multiple images
  Future<List<File>> compressMultipleImages({
    required List<File> imageFiles,
    int maxWidth = 1024,
    int maxHeight = 1024,
    int quality = 85,
    Function(int, int)? onProgress,
  }) async {
    final List<File> compressedFiles = [];
    
    for (int i = 0; i < imageFiles.length; i++) {
      try {
        final File compressedFile = await compressImage(
          imageFile: imageFiles[i],
          maxWidth: maxWidth,
          maxHeight: maxHeight,
          quality: quality,
        );
        
        compressedFiles.add(compressedFile);
        
        if (onProgress != null) {
          onProgress(i + 1, imageFiles.length);
        }
      } catch (e) {
        print('Failed to compress image ${i + 1}: $e');
        // Add original file if compression fails
        compressedFiles.add(imageFiles[i]);
      }
    }
    
    return compressedFiles;
  }

  /// Clean up temporary compressed files
  Future<void> cleanupTempFiles(List<File> tempFiles) async {
    for (final File file in tempFiles) {
      try {
        if (file.existsSync()) {
          await file.delete();
        }
      } catch (e) {
        print('Failed to delete temp file: ${file.path}, error: $e');
      }
    }
  }

  /// Get image file info
  Future<ImageFileInfo> getImageInfo(File imageFile) async {
    try {
      final int fileSizeInBytes = imageFile.lengthSync();
      final String fileName = path.basename(imageFile.path);
      final String fileExtension = path.extension(imageFile.path);
      
      return ImageFileInfo(
        fileName: fileName,
        fileExtension: fileExtension,
        fileSizeInBytes: fileSizeInBytes,
        fileSizeInKB: fileSizeInBytes ~/ 1024,
        fileSizeInMB: fileSizeInBytes / (1024 * 1024),
        filePath: imageFile.path,
      );
    } catch (e) {
      throw ImageCompressionException('Failed to get image info: $e');
    }
  }
}

/// Image file information class
class ImageFileInfo {
  final String fileName;
  final String fileExtension;
  final int fileSizeInBytes;
  final int fileSizeInKB;
  final double fileSizeInMB;
  final String filePath;
  final int? width;
  final int? height;

  ImageFileInfo({
    required this.fileName,
    required this.fileExtension,
    required this.fileSizeInBytes,
    required this.fileSizeInKB,
    required this.fileSizeInMB,
    required this.filePath,
    this.width,
    this.height,
  });

  @override
  String toString() {
    return 'ImageFileInfo(fileName: $fileName, size: ${fileSizeInMB.toStringAsFixed(2)}MB, dimensions: ${width ?? '?'}x${height ?? '?'})';
  }
}

/// Custom exception for image compression errors
class ImageCompressionException implements Exception {
  final String message;
  
  ImageCompressionException(this.message);
  
  @override
  String toString() => 'ImageCompressionException: $message';
}

/// Riverpod provider for ImageCompressionService
final imageCompressionServiceProvider = Provider<ImageCompressionService>((ref) {
  return ImageCompressionService();
});
