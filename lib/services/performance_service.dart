import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class PerformanceService {
  final FirebasePerformance _performance = FirebasePerformance.instance;

  /// Start a custom trace
  Future<Trace> startTrace(String traceName) async {
    final trace = _performance.newTrace(traceName);
    await trace.start();
    return trace;
  }

  /// Stop a custom trace
  Future<void> stopTrace(Trace trace) async {
    await trace.stop();
  }

  /// Create and track a custom trace with automatic cleanup
  Future<T> trackTrace<T>(
    String traceName,
    Future<T> Function() operation,
  ) async {
    final trace = await startTrace(traceName);
    try {
      final result = await operation();
      return result;
    } finally {
      await stopTrace(trace);
    }
  }

  /// Track screen loading time
  Future<T> trackScreenLoad<T>(
    String screenName,
    Future<T> Function() loadOperation,
  ) async {
    return trackTrace('screen_load_$screenName', loadOperation);
  }

  /// Track API call performance
  Future<T> trackApiCall<T>(
    String apiName,
    Future<T> Function() apiCall,
  ) async {
    return trackTrace('api_call_$apiName', apiCall);
  }

  /// Track database operation performance
  Future<T> trackDatabaseOperation<T>(
    String operation,
    Future<T> Function() dbOperation,
  ) async {
    return trackTrace('db_operation_$operation', dbOperation);
  }

  /// Track authentication operations
  Future<T> trackAuthOperation<T>(
    String operation,
    Future<T> Function() authOperation,
  ) async {
    return trackTrace('auth_operation_$operation', authOperation);
  }

  /// Track payment operations
  Future<T> trackPaymentOperation<T>(
    String operation,
    Future<T> Function() paymentOperation,
  ) async {
    return trackTrace('payment_operation_$operation', paymentOperation);
  }

  /// Track image loading performance
  Future<T> trackImageLoad<T>(
    String imageType,
    Future<T> Function() imageLoad,
  ) async {
    return trackTrace('image_load_$imageType', imageLoad);
  }

  /// Track cart operations
  Future<T> trackCartOperation<T>(
    String operation,
    Future<T> Function() cartOperation,
  ) async {
    return trackTrace('cart_operation_$operation', cartOperation);
  }

  /// Track order operations
  Future<T> trackOrderOperation<T>(
    String operation,
    Future<T> Function() orderOperation,
  ) async {
    return trackTrace('order_operation_$operation', orderOperation);
  }

  /// Add custom attributes to a trace
  void addTraceAttribute(Trace trace, String key, String value) {
    trace.putAttribute(key, value);
  }

  /// Add custom metrics to a trace
  void addTraceMetric(Trace trace, String metricName, int value) {
    trace.setMetric(metricName, value);
  }

  /// Increment a metric in a trace
  void incrementTraceMetric(Trace trace, String metricName, int incrementBy) {
    trace.incrementMetric(metricName, incrementBy);
  }

  /// Track app startup performance
  Future<void> trackAppStartup() async {
    final trace = await startTrace('app_startup');
    addTraceAttribute(trace, 'startup_type', 'cold_start');
    // This will be stopped when the first screen is fully loaded
    return;
  }

  /// Track navigation performance
  Future<T> trackNavigation<T>(
    String fromScreen,
    String toScreen,
    Future<T> Function() navigation,
  ) async {
    final trace = await startTrace('navigation_${fromScreen}_to_$toScreen');
    addTraceAttribute(trace, 'from_screen', fromScreen);
    addTraceAttribute(trace, 'to_screen', toScreen);

    try {
      final result = await navigation();
      return result;
    } finally {
      await stopTrace(trace);
    }
  }

  /// Track form submission performance
  Future<T> trackFormSubmission<T>(
    String formName,
    Future<T> Function() submission,
  ) async {
    return trackTrace('form_submission_$formName', submission);
  }

  /// Track search operations
  Future<T> trackSearch<T>(
    String searchType,
    Future<T> Function() searchOperation,
  ) async {
    return trackTrace('search_$searchType', searchOperation);
  }

  /// Track file upload performance
  Future<T> trackFileUpload<T>(
    String fileType,
    int fileSizeBytes,
    Future<T> Function() uploadOperation,
  ) async {
    final trace = await startTrace('file_upload_$fileType');
    addTraceMetric(trace, 'file_size_bytes', fileSizeBytes);

    try {
      final result = await uploadOperation();
      return result;
    } finally {
      await stopTrace(trace);
    }
  }

  /// Track network request performance
  Future<T> trackNetworkRequest<T>(
    String endpoint,
    String method,
    Future<T> Function() networkRequest,
  ) async {
    final trace = await startTrace(
      'network_request_${method.toLowerCase()}_${endpoint.replaceAll('/', '_')}',
    );
    addTraceAttribute(trace, 'endpoint', endpoint);
    addTraceAttribute(trace, 'method', method);

    try {
      final result = await networkRequest();
      addTraceAttribute(trace, 'status', 'success');
      return result;
    } catch (e) {
      addTraceAttribute(trace, 'status', 'error');
      addTraceAttribute(trace, 'error_type', e.runtimeType.toString());
      rethrow;
    } finally {
      await stopTrace(trace);
    }
  }

  /// Enable/disable performance monitoring
  Future<void> setPerformanceCollectionEnabled(bool enabled) async {
    await _performance.setPerformanceCollectionEnabled(enabled);
  }

  /// Check if performance monitoring is enabled
  Future<bool> get isPerformanceCollectionEnabled {
    return _performance.isPerformanceCollectionEnabled();
  }

  /// Create HTTP metric for manual network tracking
  HttpMetric newHttpMetric(String url, HttpMethod httpMethod) {
    return _performance.newHttpMetric(url, httpMethod);
  }

  /// Track widget build performance
  Future<T> trackWidgetBuild<T>(
    String widgetName,
    Future<T> Function() buildOperation,
  ) async {
    return trackTrace('widget_build_$widgetName', buildOperation);
  }

  /// Track animation performance
  Future<T> trackAnimation<T>(
    String animationName,
    Future<T> Function() animationOperation,
  ) async {
    return trackTrace('animation_$animationName', animationOperation);
  }

  /// Track user interaction performance
  Future<T> trackUserInteraction<T>(
    String interactionType,
    Future<T> Function() interaction,
  ) async {
    return trackTrace('user_interaction_$interactionType', interaction);
  }

  /// Track cache operations
  Future<T> trackCacheOperation<T>(
    String operation,
    Future<T> Function() cacheOperation,
  ) async {
    return trackTrace('cache_operation_$operation', cacheOperation);
  }

  /// Track background task performance
  Future<T> trackBackgroundTask<T>(
    String taskName,
    Future<T> Function() backgroundTask,
  ) async {
    return trackTrace('background_task_$taskName', backgroundTask);
  }

  /// Add performance markers for debugging
  Future<void> addPerformanceMarker(
    String markerName, {
    Map<String, String>? attributes,
  }) async {
    final trace = await startTrace('marker_$markerName');

    if (attributes != null) {
      for (final entry in attributes.entries) {
        addTraceAttribute(trace, entry.key, entry.value);
      }
    }

    await stopTrace(trace);
  }

  /// Track memory-intensive operations
  Future<T> trackMemoryIntensiveOperation<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    final trace = await startTrace('memory_intensive_$operationName');

    try {
      final result = await operation();
      return result;
    } finally {
      await stopTrace(trace);
    }
  }

  /// Track data processing operations
  Future<T> trackDataProcessing<T>(
    String processingType,
    int dataSize,
    Future<T> Function() processing,
  ) async {
    final trace = await startTrace('data_processing_$processingType');
    addTraceMetric(trace, 'data_size', dataSize);

    try {
      final result = await processing();
      return result;
    } finally {
      await stopTrace(trace);
    }
  }
}

// Riverpod provider for PerformanceService
final performanceServiceProvider = Provider<PerformanceService>((ref) {
  return PerformanceService();
});
