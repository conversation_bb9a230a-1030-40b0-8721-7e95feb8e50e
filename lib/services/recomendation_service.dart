import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user.dart';
import '../models/menu_item.dart';
import '../models/order.dart';

class RecommendationService {
  List<MenuItem> getRecommendations(AppUser user, List<MenuItem> menuItems) {
    // Get available menu items only
    final availableItems = menuItems.where((item) => item.isAvailable).toList();

    // Create a scoring system for recommendations
    final Map<String, double> itemScores = {};

    // Initialize scores for all items
    for (final item in availableItems) {
      itemScores[item.id] = 0.0;
    }

    // Score based on user preferences (weight: 3.0)
    _scoreByPreferences(user, availableItems, itemScores);

    // Score based on past order analysis (weight: 4.0)
    _scoreByOrderHistory(user, availableItems, itemScores);

    // Score based on popularity and favorites (weight: 2.0)
    _scoreByPopularityAndFavorites(user, availableItems, itemScores);

    // Score based on category diversity (weight: 1.0)
    _scoreByDiversity(user, availableItems, itemScores);

    // Sort items by score and return top recommendations
    final sortedItems =
        availableItems
            .where((item) => (itemScores[item.id] ?? 0.0) > 0)
            .toList()
          ..sort(
            (a, b) =>
                (itemScores[b.id] ?? 0.0).compareTo(itemScores[a.id] ?? 0.0),
          );

    // Return top 10 recommendations
    return sortedItems.take(10).toList();
  }

  /// Score items based on user preferences (vegetarian, spicy, etc.)
  void _scoreByPreferences(
    AppUser user,
    List<MenuItem> items,
    Map<String, double> scores,
  ) {
    const double preferenceWeight = 3.0;

    for (final item in items) {
      double preferenceScore = 0.0;

      // Check if item matches user preferences
      for (final preference in user.preferences) {
        if (item.tags.contains(preference.toLowerCase())) {
          preferenceScore += 1.0;
        }

        // Special handling for common preferences
        switch (preference.toLowerCase()) {
          case 'vegetarian':
          case 'vegie':
            if (item.isVegetarian) preferenceScore += 1.5;
            break;
          case 'spicy':
            if (item.isSpicy) preferenceScore += 1.5;
            break;
          case 'healthy':
            if (item.calories > 0 && item.calories < 400) {
              preferenceScore += 1.0;
            }
            break;
        }
      }

      scores[item.id] =
          (scores[item.id] ?? 0.0) + (preferenceScore * preferenceWeight);
    }
  }

  /// Score items based on past order analysis
  void _scoreByOrderHistory(
    AppUser user,
    List<MenuItem> items,
    Map<String, double> scores,
  ) {
    const double orderHistoryWeight = 4.0;

    if (user.orderHistory.isEmpty) return;

    // Analyze past orders to find patterns
    final Map<String, int> itemFrequency = {};
    final Map<String, int> categoryFrequency = {};
    final Set<String> orderedItemIds = {};

    // Count frequency of ordered items and categories
    for (final order in user.orderHistory) {
      // Only consider completed orders
      if (order.status == OrderStatus.completed ||
          order.status == OrderStatus.delivered) {
        for (final orderItem in order.items) {
          final menuItemId = orderItem['menuItemId'] as String?;
          if (menuItemId != null) {
            orderedItemIds.add(menuItemId);
            itemFrequency[menuItemId] = (itemFrequency[menuItemId] ?? 0) + 1;

            // Find the menu item to get its category
            final menuItem = items.firstWhere(
              (item) => item.id == menuItemId,
              orElse: () => items.first,
            );
            categoryFrequency[menuItem.category] =
                (categoryFrequency[menuItem.category] ?? 0) + 1;
          }
        }
      }
    }

    // Score items based on order history patterns
    for (final item in items) {
      double historyScore = 0.0;

      // Higher score for frequently ordered items (but not too high to avoid repetition)
      if (itemFrequency.containsKey(item.id)) {
        historyScore += (itemFrequency[item.id]! * 0.5).clamp(0.0, 2.0);
      }

      // Score items from frequently ordered categories
      if (categoryFrequency.containsKey(item.category)) {
        historyScore += (categoryFrequency[item.category]! * 0.3).clamp(
          0.0,
          1.5,
        );
      }

      // Boost score for items similar to previously ordered ones
      for (final orderedItemId in orderedItemIds) {
        final orderedItem = items.firstWhere(
          (i) => i.id == orderedItemId,
          orElse: () => items.first,
        );

        // Same category bonus
        if (item.category == orderedItem.category &&
            item.id != orderedItem.id) {
          historyScore += 0.5;
        }

        // Similar tags bonus
        final commonTags =
            item.tags.where((tag) => orderedItem.tags.contains(tag)).length;
        historyScore += commonTags * 0.2;
      }

      scores[item.id] =
          (scores[item.id] ?? 0.0) + (historyScore * orderHistoryWeight);
    }
  }

  /// Score items based on popularity and user favorites
  void _scoreByPopularityAndFavorites(
    AppUser user,
    List<MenuItem> items,
    Map<String, double> scores,
  ) {
    const double popularityWeight = 2.0;
    const double favoriteWeight = 5.0;

    for (final item in items) {
      double popularityScore = 0.0;

      // High score for user's favorite items
      if (user.favorites.contains(item.id)) {
        popularityScore += favoriteWeight;
      }

      // Score for popular items
      if (item.isPopular) {
        popularityScore += 1.5;
      }

      scores[item.id] =
          (scores[item.id] ?? 0.0) + (popularityScore * popularityWeight);
    }
  }

  /// Score items to promote category diversity
  void _scoreByDiversity(
    AppUser user,
    List<MenuItem> items,
    Map<String, double> scores,
  ) {
    const double diversityWeight = 1.0;

    // Get categories from recent orders
    final Set<String> recentCategories = {};
    if (user.orderHistory.isNotEmpty) {
      final recentOrders =
          user.orderHistory
              .where(
                (order) => order.createdAt.isAfter(
                  DateTime.now().subtract(const Duration(days: 30)),
                ),
              )
              .toList();

      for (final order in recentOrders) {
        for (final orderItem in order.items) {
          final menuItemId = orderItem['menuItemId'] as String?;
          if (menuItemId != null) {
            final menuItem = items.firstWhere(
              (item) => item.id == menuItemId,
              orElse: () => items.first,
            );
            recentCategories.add(menuItem.category);
          }
        }
      }
    }

    // Boost items from categories not recently ordered
    for (final item in items) {
      double diversityScore = 0.0;

      if (!recentCategories.contains(item.category)) {
        diversityScore += 1.0; // Encourage trying new categories
      }

      scores[item.id] =
          (scores[item.id] ?? 0.0) + (diversityScore * diversityWeight);
    }
  }

  List<MenuItem> getSuggestedAddOns(
    List<MenuItem> menuItems,
    String category,
    String excludeItemId,
  ) {
    // Get items from the same category, excluding the current item
    final categoryItems =
        menuItems
            .where(
              (item) =>
                  item.category == category &&
                  item.id != excludeItemId &&
                  item.isAvailable,
            )
            .toList();

    // If we have items in the same category, return them
    if (categoryItems.isNotEmpty) {
      // Prioritize popular items
      categoryItems.sort((a, b) {
        if (a.isPopular && !b.isPopular) return -1;
        if (!a.isPopular && b.isPopular) return 1;
        return a.name.compareTo(b.name);
      });
      return categoryItems.take(3).toList();
    }

    // If no items in same category, suggest complementary items
    return _getComplementaryItems(menuItems, category, excludeItemId);
  }

  /// Get items that complement the given category
  List<MenuItem> _getComplementaryItems(
    List<MenuItem> menuItems,
    String category,
    String excludeItemId,
  ) {
    final complementaryCategories = <String>[];

    // Define complementary relationships
    switch (category.toLowerCase()) {
      case 'main course':
      case 'mains':
        complementaryCategories.addAll(['appetizers', 'sides', 'beverages']);
        break;
      case 'appetizers':
      case 'starters':
        complementaryCategories.addAll(['main course', 'mains', 'beverages']);
        break;
      case 'desserts':
        complementaryCategories.addAll(['beverages', 'coffee']);
        break;
      case 'beverages':
        complementaryCategories.addAll(['desserts', 'snacks']);
        break;
      default:
        complementaryCategories.addAll(['beverages', 'sides']);
    }

    final complementaryItems =
        menuItems
            .where(
              (item) =>
                  complementaryCategories.contains(
                    item.category.toLowerCase(),
                  ) &&
                  item.id != excludeItemId &&
                  item.isAvailable,
            )
            .toList();

    // Sort by popularity and price (prefer lower priced add-ons)
    complementaryItems.sort((a, b) {
      if (a.isPopular && !b.isPopular) return -1;
      if (!a.isPopular && b.isPopular) return 1;
      return a.price.compareTo(b.price);
    });

    return complementaryItems.take(3).toList();
  }

  /// Get personalized recommendations for a specific category
  List<MenuItem> getCategoryRecommendations(
    AppUser user,
    List<MenuItem> menuItems,
    String category, {
    int limit = 5,
  }) {
    final categoryItems =
        menuItems
            .where(
              (item) =>
                  item.category.toLowerCase() == category.toLowerCase() &&
                  item.isAvailable,
            )
            .toList();

    if (categoryItems.isEmpty) return [];

    // Use the same scoring system but only for this category
    final Map<String, double> itemScores = {};

    for (final item in categoryItems) {
      itemScores[item.id] = 0.0;
    }

    _scoreByPreferences(user, categoryItems, itemScores);
    _scoreByOrderHistory(user, categoryItems, itemScores);
    _scoreByPopularityAndFavorites(user, categoryItems, itemScores);

    // Sort and return top items
    categoryItems.sort(
      (a, b) => (itemScores[b.id] ?? 0.0).compareTo(itemScores[a.id] ?? 0.0),
    );

    return categoryItems.take(limit).toList();
  }

  /// Get trending items based on recent order patterns
  List<MenuItem> getTrendingItems(List<MenuItem> menuItems, {int limit = 5}) {
    // For now, return popular items
    // In a real app, this would analyze recent order data across all users
    final trendingItems =
        menuItems.where((item) => item.isAvailable && item.isPopular).toList();

    trendingItems.sort(
      (a, b) => b.name.compareTo(a.name),
    ); // Simple sort for demo

    return trendingItems.take(limit).toList();
  }

  /// Get items similar to a given item
  List<MenuItem> getSimilarItems(
    MenuItem targetItem,
    List<MenuItem> menuItems, {
    int limit = 4,
  }) {
    final similarItems =
        menuItems
            .where(
              (item) =>
                  item.id != targetItem.id &&
                  item.isAvailable &&
                  (item.category == targetItem.category ||
                      item.tags.any((tag) => targetItem.tags.contains(tag))),
            )
            .toList();

    // Score by similarity
    similarItems.sort((a, b) {
      int scoreA = 0;
      int scoreB = 0;

      // Same category bonus
      if (a.category == targetItem.category) scoreA += 3;
      if (b.category == targetItem.category) scoreB += 3;

      // Common tags bonus
      scoreA += a.tags.where((tag) => targetItem.tags.contains(tag)).length;
      scoreB += b.tags.where((tag) => targetItem.tags.contains(tag)).length;

      // Price similarity bonus (prefer items within 20% price range)
      final priceRangeA = (a.price - targetItem.price).abs() / targetItem.price;
      final priceRangeB = (b.price - targetItem.price).abs() / targetItem.price;

      if (priceRangeA < 0.2) scoreA += 1;
      if (priceRangeB < 0.2) scoreB += 1;

      return scoreB.compareTo(scoreA);
    });

    return similarItems.take(limit).toList();
  }
}

// Riverpod provider for RecommendationService
final recommendationServiceProvider = Provider<RecommendationService>((ref) {
  return RecommendationService();
});
