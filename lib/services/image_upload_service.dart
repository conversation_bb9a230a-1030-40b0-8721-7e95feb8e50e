// File: lib/services/image_upload_service.dart
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path/path.dart' as path;
import 'package:smart_restaurant_menu/config/supabase_config.dart';
import 'package:smart_restaurant_menu/services/supabase_storage_service.dart';

/// Validation result for image uploads
class ImageValidationResult {
  final bool isValid;
  final String? error;

  ImageValidationResult({required this.isValid, this.error});
}

/// Service for handling image uploads with validation and progress tracking
class ImageUploadService {
  /// Validate an image file before upload
  ImageValidationResult validateImage(File imageFile) {
    try {
      // Check if file exists
      if (!imageFile.existsSync()) {
        return ImageValidationResult(
          isValid: false,
          error: 'Image file does not exist',
        );
      }

      // Check file size
      final fileSize = imageFile.lengthSync();
      if (fileSize > SupabaseConfig.maxImageSize) {
        final maxSizeMB = SupabaseConfig.maxImageSize / (1024 * 1024);
        return ImageValidationResult(
          isValid: false,
          error: 'Image size exceeds ${maxSizeMB.toStringAsFixed(1)}MB limit',
        );
      }

      // Check file extension
      final extension = path.extension(imageFile.path).toLowerCase().substring(1);
      if (!SupabaseConfig.allowedImageExtensions.contains(extension)) {
        return ImageValidationResult(
          isValid: false,
          error: 'Invalid image format. Allowed: ${SupabaseConfig.allowedImageExtensions.join(', ')}',
        );
      }

      return ImageValidationResult(isValid: true);
    } catch (e) {
      return ImageValidationResult(
        isValid: false,
        error: 'Error validating image: $e',
      );
    }
  }

  /// Upload a general image with progress tracking
  Future<String?> uploadImage({
    required File imageFile,
    String? folder,
    String? fileName,
    Function(double)? onProgress,
  }) async {
    try {
      // Validate image first
      final validationResult = validateImage(imageFile);
      if (!validationResult.isValid) {
        throw Exception(validationResult.error);
      }

      // Simulate progress updates (Supabase doesn't provide real progress)
      if (onProgress != null) {
        onProgress(0.1);
      }

      String? customPath;
      if (folder != null || fileName != null) {
        final extension = path.extension(imageFile.path).toLowerCase().substring(1);
        final finalFileName = fileName ?? 'image_${DateTime.now().millisecondsSinceEpoch}.$extension';
        customPath = folder != null ? '$folder/$finalFileName' : finalFileName;
      }

      if (onProgress != null) {
        onProgress(0.5);
      }

      final result = await SupabaseStorageService.uploadImage(
        file: imageFile,
        customPath: customPath,
      );

      if (onProgress != null) {
        onProgress(1.0);
      }

      return result;
    } catch (e) {
      debugPrint('ImageUploadService: Error uploading image: $e');
      return null;
    }
  }

  /// Upload menu item image with progress tracking
  Future<String?> uploadMenuItemImage({
    required File imageFile,
    String? menuItemId,
    Function(double)? onProgress,
  }) async {
    try {
      // Validate image first
      final validationResult = validateImage(imageFile);
      if (!validationResult.isValid) {
        throw Exception(validationResult.error);
      }

      // Simulate progress updates
      if (onProgress != null) {
        onProgress(0.1);
      }

      if (onProgress != null) {
        onProgress(0.5);
      }

      final result = await SupabaseStorageService.uploadMenuItemImage(
        imageFile,
        menuItemId: menuItemId,
      );

      if (onProgress != null) {
        onProgress(1.0);
      }

      return result;
    } catch (e) {
      debugPrint('ImageUploadService: Error uploading menu item image: $e');
      return null;
    }
  }

  /// Upload profile image with progress tracking
  Future<String?> uploadProfileImage({
    required String userId,
    required File imageFile,
    Function(double)? onProgress,
  }) async {
    try {
      // Validate image first
      final validationResult = validateImage(imageFile);
      if (!validationResult.isValid) {
        throw Exception(validationResult.error);
      }

      // Simulate progress updates
      if (onProgress != null) {
        onProgress(0.1);
      }

      if (onProgress != null) {
        onProgress(0.5);
      }

      final result = await SupabaseStorageService.uploadProfileImage(userId, imageFile);

      if (onProgress != null) {
        onProgress(1.0);
      }

      return result;
    } catch (e) {
      debugPrint('ImageUploadService: Error uploading profile image: $e');
      return null;
    }
  }

  /// Upload image from bytes with validation
  Future<String?> uploadImageFromBytes({
    required Uint8List bytes,
    required String fileName,
    String? folder,
    Function(double)? onProgress,
  }) async {
    try {
      // Basic validation for bytes
      if (bytes.isEmpty) {
        throw Exception('Image data is empty');
      }

      if (bytes.length > SupabaseConfig.maxImageSize) {
        final maxSizeMB = SupabaseConfig.maxImageSize / (1024 * 1024);
        throw Exception('Image size exceeds ${maxSizeMB.toStringAsFixed(1)}MB limit');
      }

      // Simulate progress updates
      if (onProgress != null) {
        onProgress(0.1);
      }

      final finalFileName = folder != null ? '$folder/$fileName' : fileName;

      if (onProgress != null) {
        onProgress(0.5);
      }

      final result = await SupabaseStorageService.uploadImageFromBytes(
        bytes: bytes,
        fileName: finalFileName,
      );

      if (onProgress != null) {
        onProgress(1.0);
      }

      return result;
    } catch (e) {
      debugPrint('ImageUploadService: Error uploading image from bytes: $e');
      return null;
    }
  }
}

/// Riverpod provider for ImageUploadService
final imageUploadServiceProvider = Provider<ImageUploadService>((ref) {
  return ImageUploadService();
});
