// Mock Stripe service for POC development
// This replaces the actual Stripe integration for testing purposes

class MockStripe {
  static void init({required String publishableKey}) {
    // Mock initialization
    print('Mock Stripe initialized with key: ${publishableKey.substring(0, 10)}...');
  }

  static Future<void> initPaymentSheet({
    required PaymentSheetInitParams params,
  }) async {
    // Mock payment sheet initialization
    await Future.delayed(const Duration(milliseconds: 500));
    print('Mock payment sheet initialized');
  }

  static Future<PaymentSheetResult> presentPaymentSheet() async {
    // Mock payment sheet presentation
    await Future.delayed(const Duration(seconds: 2));
    print('Mock payment sheet presented and completed');
    return PaymentSheetResult.completed;
  }

  static Future<bool> isApplePaySupported() async {
    // Mock Apple Pay support check
    return true;
  }

  static Future<bool> isGooglePaySupported() async {
    // Mock Google Pay support check
    return true;
  }
}

// Mock classes to replace Stripe types
class PaymentSheetInitParams {
  final String? paymentIntentClientSecret;
  final String? setupIntentClientSecret;
  final PaymentSheetAppearance? appearance;
  final BillingDetails? defaultBillingDetails;
  final String? merchantDisplayName;
  final String? customerId;
  final String? customerEphemeralKeySecret;
  final bool? allowsDelayedPaymentMethods;

  PaymentSheetInitParams({
    this.paymentIntentClientSecret,
    this.setupIntentClientSecret,
    this.appearance,
    this.defaultBillingDetails,
    this.merchantDisplayName,
    this.customerId,
    this.customerEphemeralKeySecret,
    this.allowsDelayedPaymentMethods,
  });
}

class PaymentSheetAppearance {
  final PaymentSheetColors? colors;
  final PaymentSheetShapes? shapes;
  final PaymentSheetPrimaryButton? primaryButton;

  PaymentSheetAppearance({
    this.colors,
    this.shapes,
    this.primaryButton,
  });
}

class PaymentSheetColors {
  final int? primary;
  final int? background;
  final int? componentBackground;
  final int? componentBorder;
  final int? componentDivider;
  final int? primaryText;
  final int? secondaryText;
  final int? componentText;
  final int? placeholderText;
  final int? icon;
  final int? error;

  PaymentSheetColors({
    this.primary,
    this.background,
    this.componentBackground,
    this.componentBorder,
    this.componentDivider,
    this.primaryText,
    this.secondaryText,
    this.componentText,
    this.placeholderText,
    this.icon,
    this.error,
  });
}

class PaymentSheetShapes {
  final double? borderRadius;
  final double? borderWidth;

  PaymentSheetShapes({
    this.borderRadius,
    this.borderWidth,
  });
}

class PaymentSheetPrimaryButton {
  final PaymentSheetPrimaryButtonAppearance? appearance;

  PaymentSheetPrimaryButton({
    this.appearance,
  });
}

class PaymentSheetPrimaryButtonAppearance {
  final int? backgroundColor;
  final int? textColor;
  final double? borderRadius;

  PaymentSheetPrimaryButtonAppearance({
    this.backgroundColor,
    this.textColor,
    this.borderRadius,
  });
}

class BillingDetails {
  final String? email;
  final String? phone;
  final Address? address;
  final String? name;

  BillingDetails({
    this.email,
    this.phone,
    this.address,
    this.name,
  });
}

class Address {
  final String? city;
  final String? country;
  final String? line1;
  final String? line2;
  final String? postalCode;
  final String? state;

  Address({
    this.city,
    this.country,
    this.line1,
    this.line2,
    this.postalCode,
    this.state,
  });
}

enum PaymentSheetResult {
  completed,
  canceled,
  failed,
}
