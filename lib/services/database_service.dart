import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:smart_restaurant_menu/models/order.dart';
import '../models/user.dart';
import '../models/menu_item.dart';

class DatabaseService {
  final FirebaseFirestore _db = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  Future<void> saveUser(AppUser user) async {
    await _db.collection('users').doc(user.uid).set(user.toMap());
  }

  Future<AppUser?> getUser(String uid) async {
    print('DB: Fetching user data for UID: $uid');
    DocumentSnapshot doc = await _db.collection('users').doc(uid).get();
    if (doc.exists) {
      final userData = doc.data() as Map<String, dynamic>;
      print(
        'DB: User document found - Role: ${userData['role']}, Email: ${userData['email']}',
      );
      return AppUser.fromMap(userData);
    } else {
      print('DB: User document not found for UID: $uid');
      return null;
    }
  }

  Future<List<MenuItem>> getMenuItems() async {
    QuerySnapshot snapshot = await _db.collection('menu_items').get();
    return snapshot.docs
        .map((doc) => MenuItem.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
  }

  Future<List<MenuItem>> getSuggestedAddOns(
    String category,
    String excludeItemId,
  ) async {
    print('Fetching suggested add-ons for category: $category');
    print('Excluding item ID: $excludeItemId');
    QuerySnapshot snapshot =
        await _db
            .collection('menu_items')
            .where('category', isEqualTo: category)
            .limit(3)
            .get();
    print('Number of suggested add-ons: ${snapshot.docs.length}');
    return snapshot.docs
        .map((doc) => MenuItem.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
  }

  Future<void> placeOrder(OrderModel order) async {
    await _db.collection('orders').doc(order.id).set(order.toMap());
  }

  Future<List<OrderModel>> getUserOrders(String userId) async {
    QuerySnapshot snapshot =
        await _db.collection('orders').where('userId', isEqualTo: userId).get();
    return snapshot.docs
        .map((doc) => OrderModel.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
  }

  Future<void> updateOrderStatus(
    String orderId, {
    String status = 'Cancelled',
  }) async {
    await _db.collection('orders').doc(orderId).update({'status': status});
  }

  // Update user profile
  Future<void> updateUserProfile(Map<String, dynamic> profileData) async {
    final user = _auth.currentUser;
    if (user != null) {
      await _db.collection('users').doc(user.uid).update(profileData);
    }
  }

  // Update user favorites
  Future<void> updateUserFavorites(Map<String, dynamic> favoritesData) async {
    final user = _auth.currentUser;
    if (user != null) {
      await _db.collection('users').doc(user.uid).update({
        'favorites': favoritesData,
        'updatedAt': DateTime.now().toIso8601String(),
      });
    }
  }
}

// Riverpod provider for DatabaseService
final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService();
});
