import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/order.dart';
import '../models/menu_item.dart';

class AnalyticsService {
  final FirebaseFirestore _db = FirebaseFirestore.instance;

  // Get comprehensive dashboard analytics
  Future<Map<String, dynamic>> getDashboardAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final start =
        startDate ?? DateTime.now().subtract(const Duration(days: 30));
    final end = endDate ?? DateTime.now();

    try {
      // Get orders data
      final ordersSnapshot =
          await _db
              .collection('orders')
              .where(
                'createdAt',
                isGreaterThanOrEqualTo: start.toIso8601String(),
              )
              .where('createdAt', isLessThanOrEqualTo: end.toIso8601String())
              .get();

      final orders =
          ordersSnapshot.docs
              .map((doc) => OrderModel.fromMap(doc.data()))
              .toList();

      // Get menu items data
      final menuSnapshot = await _db.collection('menu_items').get();
      final menuItems =
          menuSnapshot.docs.map((doc) => MenuItem.fromMap(doc.data())).toList();

      // Calculate analytics
      final analytics = await _calculateComprehensiveAnalytics(
        orders,
        menuItems,
        start,
        end,
      );

      return analytics;
    } catch (e) {
      // Return mock data if there's an error (for development/testing)
      return _getMockDashboardAnalytics();
    }
  }

  // Get sales analytics
  Future<Map<String, dynamic>> getSalesAnalytics({
    DateTime? startDate,
    DateTime? endDate,
    String period = 'daily', // daily, weekly, monthly
  }) async {
    final start =
        startDate ?? DateTime.now().subtract(const Duration(days: 30));
    final end = endDate ?? DateTime.now();

    try {
      final ordersSnapshot =
          await _db
              .collection('orders')
              .where(
                'createdAt',
                isGreaterThanOrEqualTo: start.toIso8601String(),
              )
              .where('createdAt', isLessThanOrEqualTo: end.toIso8601String())
              .where(
                'status',
                whereIn: [
                  OrderStatus.completed.name,
                  OrderStatus.delivered.name,
                ],
              )
              .get();

      final orders =
          ordersSnapshot.docs
              .map((doc) => OrderModel.fromMap(doc.data()))
              .toList();

      return _calculateSalesAnalytics(orders, start, end, period);
    } catch (e) {
      return _getMockSalesAnalytics(period);
    }
  }

  // Get popular items analytics
  Future<Map<String, dynamic>> getPopularItemsAnalytics({
    DateTime? startDate,
    DateTime? endDate,
    int limit = 10,
  }) async {
    final start =
        startDate ?? DateTime.now().subtract(const Duration(days: 30));
    final end = endDate ?? DateTime.now();

    try {
      final ordersSnapshot =
          await _db
              .collection('orders')
              .where(
                'createdAt',
                isGreaterThanOrEqualTo: start.toIso8601String(),
              )
              .where('createdAt', isLessThanOrEqualTo: end.toIso8601String())
              .where(
                'status',
                whereIn: [
                  OrderStatus.completed.name,
                  OrderStatus.delivered.name,
                ],
              )
              .get();

      final orders =
          ordersSnapshot.docs
              .map((doc) => OrderModel.fromMap(doc.data()))
              .toList();

      return _calculatePopularItemsAnalytics(orders, limit);
    } catch (e) {
      return _getMockPopularItemsAnalytics(limit);
    }
  }

  // Get customer analytics
  Future<Map<String, dynamic>> getCustomerAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final start =
        startDate ?? DateTime.now().subtract(const Duration(days: 30));
    final end = endDate ?? DateTime.now();

    try {
      final ordersSnapshot =
          await _db
              .collection('orders')
              .where(
                'createdAt',
                isGreaterThanOrEqualTo: start.toIso8601String(),
              )
              .where('createdAt', isLessThanOrEqualTo: end.toIso8601String())
              .get();

      final orders =
          ordersSnapshot.docs
              .map((doc) => OrderModel.fromMap(doc.data()))
              .toList();

      return _calculateCustomerAnalytics(orders, start, end);
    } catch (e) {
      return _getMockCustomerAnalytics();
    }
  }

  // Get operational analytics
  Future<Map<String, dynamic>> getOperationalAnalytics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final start =
        startDate ?? DateTime.now().subtract(const Duration(days: 30));
    final end = endDate ?? DateTime.now();

    try {
      final ordersSnapshot =
          await _db
              .collection('orders')
              .where(
                'createdAt',
                isGreaterThanOrEqualTo: start.toIso8601String(),
              )
              .where('createdAt', isLessThanOrEqualTo: end.toIso8601String())
              .get();

      final orders =
          ordersSnapshot.docs
              .map((doc) => OrderModel.fromMap(doc.data()))
              .toList();

      return _calculateOperationalAnalytics(orders, start, end);
    } catch (e) {
      return _getMockOperationalAnalytics();
    }
  }

  // Get real-time metrics
  Future<Map<String, dynamic>> getRealTimeMetrics() async {
    try {
      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
      final startOfMonth = DateTime(now.year, now.month, 1);

      // Today's metrics
      final todayOrders = await _getOrdersInPeriod(startOfDay, now);
      final weekOrders = await _getOrdersInPeriod(startOfWeek, now);
      final monthOrders = await _getOrdersInPeriod(startOfMonth, now);

      // Current pending orders
      final pendingSnapshot =
          await _db
              .collection('orders')
              .where('status', isEqualTo: OrderStatus.pending.name)
              .get();

      final pendingOrders = pendingSnapshot.docs.length;

      // Active menu items
      final menuSnapshot =
          await _db
              .collection('menu_items')
              .where('isAvailable', isEqualTo: true)
              .get();

      final activeMenuItems = menuSnapshot.docs.length;

      return {
        'todayOrders': todayOrders.length,
        'todayRevenue': todayOrders.fold<double>(
          0,
          (total, order) => total + order.total,
        ),
        'weekOrders': weekOrders.length,
        'weekRevenue': weekOrders.fold<double>(
          0,
          (total, order) => total + order.total,
        ),
        'monthOrders': monthOrders.length,
        'monthRevenue': monthOrders.fold<double>(
          0,
          (total, order) => total + order.total,
        ),
        'pendingOrders': pendingOrders,
        'activeMenuItems': activeMenuItems,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      // Return mock data if there's an error (for development/testing)
      return _getMockRealTimeMetrics();
    }
  }

  // Mock data for development/testing
  Map<String, dynamic> _getMockRealTimeMetrics() {
    return {
      'todayOrders': 12,
      'todayRevenue': 156000.0,
      'weekOrders': 89,
      'weekRevenue': 1234000.0,
      'monthOrders': 342,
      'monthRevenue': 4567000.0,
      'pendingOrders': 3,
      'activeMenuItems': 25,
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  Map<String, dynamic> _getMockDashboardAnalytics() {
    final now = DateTime.now();
    final start = now.subtract(const Duration(days: 30));

    return {
      'period': {
        'start': start.toIso8601String(),
        'end': now.toIso8601String(),
        'days': 30,
      },
      'overview': {
        'totalOrders': 342,
        'completedOrders': 298,
        'totalRevenue': 4567000.0,
        'averageOrderValue': 15325.0,
        'conversionRate': 87.1,
      },
      'distributions': {
        'orderStatus': {
          'pending': 8,
          'confirmed': 12,
          'preparing': 15,
          'ready': 9,
          'outForDelivery': 6,
          'delivered': 245,
          'completed': 53,
          'cancelled': 14,
        },
        'orderType': {'delivery': 198, 'pickup': 89, 'dineIn': 55},
      },
      'trends': {
        'dailyRevenue': _generateMockDailyRevenue(start, now),
        'hourlyOrders': _generateMockHourlyOrders(),
      },
      'menuPerformance': _getMockMenuPerformance(),
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }

  // Helper method to get orders in a specific period
  Future<List<OrderModel>> _getOrdersInPeriod(
    DateTime start,
    DateTime end,
  ) async {
    final snapshot =
        await _db
            .collection('orders')
            .where('createdAt', isGreaterThanOrEqualTo: start.toIso8601String())
            .where('createdAt', isLessThanOrEqualTo: end.toIso8601String())
            .get();

    return snapshot.docs.map((doc) => OrderModel.fromMap(doc.data())).toList();
  }

  // Calculate comprehensive analytics
  Future<Map<String, dynamic>> _calculateComprehensiveAnalytics(
    List<OrderModel> orders,
    List<MenuItem> menuItems,
    DateTime start,
    DateTime end,
  ) async {
    final completedOrders =
        orders
            .where(
              (order) =>
                  order.status == OrderStatus.completed ||
                  order.status == OrderStatus.delivered,
            )
            .toList();

    // Basic metrics
    final totalOrders = orders.length;
    final totalRevenue = completedOrders.fold<double>(
      0,
      (total, order) => total + order.total,
    );
    final averageOrderValue =
        completedOrders.isNotEmpty
            ? totalRevenue / completedOrders.length
            : 0.0;

    // Order status distribution
    final statusDistribution = <String, int>{};
    for (final status in OrderStatus.values) {
      statusDistribution[status.name] =
          orders.where((o) => o.status == status).length;
    }

    // Order type distribution
    final typeDistribution = <String, int>{};
    for (final order in orders) {
      typeDistribution[order.orderType.name] =
          (typeDistribution[order.orderType.name] ?? 0) + 1;
    }

    // Daily revenue trend
    final dailyRevenue = <String, double>{};
    for (final order in completedOrders) {
      final dateKey =
          '${order.createdAt.year}-${order.createdAt.month.toString().padLeft(2, '0')}-${order.createdAt.day.toString().padLeft(2, '0')}';
      dailyRevenue[dateKey] = (dailyRevenue[dateKey] ?? 0) + order.total;
    }

    // Peak hours analysis
    final hourlyOrders = <int, int>{};
    for (final order in orders) {
      final hour = order.createdAt.hour;
      hourlyOrders[hour] = (hourlyOrders[hour] ?? 0) + 1;
    }

    // Menu performance
    final menuPerformance = _calculateMenuPerformance(orders, menuItems);

    return {
      'period': {
        'start': start.toIso8601String(),
        'end': end.toIso8601String(),
        'days': end.difference(start).inDays,
      },
      'overview': {
        'totalOrders': totalOrders,
        'completedOrders': completedOrders.length,
        'totalRevenue': totalRevenue,
        'averageOrderValue': averageOrderValue,
        'conversionRate':
            totalOrders > 0 ? (completedOrders.length / totalOrders) * 100 : 0,
      },
      'distributions': {
        'orderStatus': statusDistribution,
        'orderType': typeDistribution,
      },
      'trends': {'dailyRevenue': dailyRevenue, 'hourlyOrders': hourlyOrders},
      'menuPerformance': menuPerformance,
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }

  // Calculate sales analytics with time series
  Map<String, dynamic> _calculateSalesAnalytics(
    List<OrderModel> orders,
    DateTime start,
    DateTime end,
    String period,
  ) {
    final timeSeries = <String, Map<String, dynamic>>{};
    final totalRevenue = orders.fold<double>(
      0,
      (total, order) => total + order.total,
    );
    final totalOrders = orders.length;

    // Group orders by period
    for (final order in orders) {
      String periodKey;
      switch (period) {
        case 'daily':
          periodKey =
              '${order.createdAt.year}-${order.createdAt.month.toString().padLeft(2, '0')}-${order.createdAt.day.toString().padLeft(2, '0')}';
          break;
        case 'weekly':
          final weekStart = order.createdAt.subtract(
            Duration(days: order.createdAt.weekday - 1),
          );
          periodKey =
              '${weekStart.year}-W${((weekStart.difference(DateTime(weekStart.year, 1, 1)).inDays) / 7).ceil()}';
          break;
        case 'monthly':
          periodKey =
              '${order.createdAt.year}-${order.createdAt.month.toString().padLeft(2, '0')}';
          break;
        default:
          periodKey = order.createdAt.toIso8601String().split('T')[0];
      }

      if (!timeSeries.containsKey(periodKey)) {
        timeSeries[periodKey] = {
          'revenue': 0.0,
          'orders': 0,
          'averageOrderValue': 0.0,
        };
      }

      timeSeries[periodKey]!['revenue'] =
          (timeSeries[periodKey]!['revenue'] as double) + order.total;
      timeSeries[periodKey]!['orders'] =
          (timeSeries[periodKey]!['orders'] as int) + 1;
    }

    // Calculate average order values
    timeSeries.forEach((key, value) {
      final revenue = value['revenue'] as double;
      final orderCount = value['orders'] as int;
      value['averageOrderValue'] = orderCount > 0 ? revenue / orderCount : 0.0;
    });

    return {
      'summary': {
        'totalRevenue': totalRevenue,
        'totalOrders': totalOrders,
        'averageOrderValue': totalOrders > 0 ? totalRevenue / totalOrders : 0.0,
        'period': period,
      },
      'timeSeries': timeSeries,
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }

  // Calculate popular items analytics
  Map<String, dynamic> _calculatePopularItemsAnalytics(
    List<OrderModel> orders,
    int limit,
  ) {
    final itemStats = <String, Map<String, dynamic>>{};

    // Aggregate item data from all orders
    for (final order in orders) {
      for (final item in order.items) {
        final itemName = item['name'] as String;
        final quantity = item['quantity'] as int;
        final price = (item['price'] as num).toDouble();
        final revenue = quantity * price;

        if (!itemStats.containsKey(itemName)) {
          itemStats[itemName] = {
            'name': itemName,
            'totalQuantity': 0,
            'totalRevenue': 0.0,
            'orderCount': 0,
            'averageQuantityPerOrder': 0.0,
          };
        }

        itemStats[itemName]!['totalQuantity'] =
            (itemStats[itemName]!['totalQuantity'] as int) + quantity;
        itemStats[itemName]!['totalRevenue'] =
            (itemStats[itemName]!['totalRevenue'] as double) + revenue;
        itemStats[itemName]!['orderCount'] =
            (itemStats[itemName]!['orderCount'] as int) + 1;
      }
    }

    // Calculate averages and sort by popularity
    final itemList = itemStats.values.toList();
    for (final item in itemList) {
      final totalQuantity = item['totalQuantity'] as int;
      final orderCount = item['orderCount'] as int;
      item['averageQuantityPerOrder'] =
          orderCount > 0 ? totalQuantity / orderCount : 0.0;
    }

    // Sort by total quantity (most popular first)
    itemList.sort(
      (a, b) =>
          (b['totalQuantity'] as int).compareTo(a['totalQuantity'] as int),
    );

    return {
      'topItems': itemList.take(limit).toList(),
      'totalUniqueItems': itemStats.length,
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }

  // Calculate customer analytics
  Map<String, dynamic> _calculateCustomerAnalytics(
    List<OrderModel> orders,
    DateTime start,
    DateTime end,
  ) {
    final customerStats = <String, Map<String, dynamic>>{};
    final uniqueCustomers = <String>{};

    // Aggregate customer data
    for (final order in orders) {
      final customerEmail = order.customerInfo?['email'] as String?;
      final customerName = order.customerInfo?['name'] as String?;

      if (customerEmail != null) {
        uniqueCustomers.add(customerEmail);

        if (!customerStats.containsKey(customerEmail)) {
          customerStats[customerEmail] = {
            'email': customerEmail,
            'name': customerName ?? 'Unknown',
            'totalOrders': 0,
            'totalSpent': 0.0,
            'averageOrderValue': 0.0,
            'firstOrder': order.createdAt.toIso8601String(),
            'lastOrder': order.createdAt.toIso8601String(),
          };
        }

        customerStats[customerEmail]!['totalOrders'] =
            (customerStats[customerEmail]!['totalOrders'] as int) + 1;
        customerStats[customerEmail]!['totalSpent'] =
            (customerStats[customerEmail]!['totalSpent'] as double) +
            order.total;

        // Update first and last order dates
        final currentFirst = DateTime.parse(
          customerStats[customerEmail]!['firstOrder'] as String,
        );
        final currentLast = DateTime.parse(
          customerStats[customerEmail]!['lastOrder'] as String,
        );

        if (order.createdAt.isBefore(currentFirst)) {
          customerStats[customerEmail]!['firstOrder'] =
              order.createdAt.toIso8601String();
        }
        if (order.createdAt.isAfter(currentLast)) {
          customerStats[customerEmail]!['lastOrder'] =
              order.createdAt.toIso8601String();
        }
      }
    }

    // Calculate averages
    for (final customer in customerStats.values) {
      final totalOrders = customer['totalOrders'] as int;
      final totalSpent = customer['totalSpent'] as double;
      customer['averageOrderValue'] =
          totalOrders > 0 ? totalSpent / totalOrders : 0.0;
    }

    // Sort customers by total spent
    final customerList = customerStats.values.toList();
    customerList.sort(
      (a, b) =>
          (b['totalSpent'] as double).compareTo(a['totalSpent'] as double),
    );

    // Calculate customer segments
    final newCustomers =
        customerList.where((c) {
          final firstOrder = DateTime.parse(c['firstOrder'] as String);
          return firstOrder.isAfter(start);
        }).length;

    final returningCustomers =
        customerList.where((c) => (c['totalOrders'] as int) > 1).length;

    return {
      'summary': {
        'totalCustomers': uniqueCustomers.length,
        'newCustomers': newCustomers,
        'returningCustomers': returningCustomers,
        'customerRetentionRate':
            uniqueCustomers.isNotEmpty
                ? (returningCustomers / uniqueCustomers.length) * 100
                : 0.0,
      },
      'topCustomers': customerList.take(10).toList(),
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }

  // Calculate operational analytics
  Map<String, dynamic> _calculateOperationalAnalytics(
    List<OrderModel> orders,
    DateTime start,
    DateTime end,
  ) {
    final statusTransitionTimes = <String, List<Duration>>{};
    final hourlyDistribution = <int, int>{};
    final dayOfWeekDistribution = <int, int>{};

    for (final order in orders) {
      // Hourly distribution
      final hour = order.createdAt.hour;
      hourlyDistribution[hour] = (hourlyDistribution[hour] ?? 0) + 1;

      // Day of week distribution (1 = Monday, 7 = Sunday)
      final dayOfWeek = order.createdAt.weekday;
      dayOfWeekDistribution[dayOfWeek] =
          (dayOfWeekDistribution[dayOfWeek] ?? 0) + 1;

      // Calculate processing times from status history
      if (order.statusHistory != null && order.statusHistory!.isNotEmpty) {
        DateTime? previousTime = order.createdAt;
        String? previousStatus = OrderStatus.pending.name;

        for (final statusEntry in order.statusHistory!) {
          final currentTime = DateTime.parse(
            statusEntry['timestamp'] as String,
          );
          final currentStatus = statusEntry['status'] as String;

          if (previousTime != null && previousStatus != null) {
            final transitionKey = '$previousStatus-$currentStatus';
            final duration = currentTime.difference(previousTime);

            if (!statusTransitionTimes.containsKey(transitionKey)) {
              statusTransitionTimes[transitionKey] = [];
            }
            statusTransitionTimes[transitionKey]!.add(duration);
          }

          previousTime = currentTime;
          previousStatus = currentStatus;
        }
      }
    }

    // Calculate average processing times
    final averageProcessingTimes = <String, double>{};
    statusTransitionTimes.forEach((transition, durations) {
      if (durations.isNotEmpty) {
        final totalMinutes = durations.fold<int>(
          0,
          (total, duration) => total + duration.inMinutes,
        );
        averageProcessingTimes[transition] = totalMinutes / durations.length;
      }
    });

    // Find peak hours
    final sortedHours =
        hourlyDistribution.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));
    final peakHours = sortedHours.take(3).map((e) => e.key).toList();

    return {
      'processingTimes': averageProcessingTimes,
      'peakHours': peakHours,
      'hourlyDistribution': hourlyDistribution,
      'dayOfWeekDistribution': dayOfWeekDistribution,
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }

  // Calculate menu performance
  Map<String, dynamic> _calculateMenuPerformance(
    List<OrderModel> orders,
    List<MenuItem> menuItems,
  ) {
    final itemPerformance = <String, Map<String, dynamic>>{};
    final categoryPerformance = <String, Map<String, dynamic>>{};

    // Initialize with all menu items
    for (final item in menuItems) {
      itemPerformance[item.name] = {
        'name': item.name,
        'category': item.category,
        'price': item.price,
        'isAvailable': item.isAvailable,
        'ordersCount': 0,
        'totalQuantity': 0,
        'totalRevenue': 0.0,
      };

      if (!categoryPerformance.containsKey(item.category)) {
        categoryPerformance[item.category] = {
          'category': item.category,
          'itemCount': 0,
          'ordersCount': 0,
          'totalRevenue': 0.0,
        };
      }
      categoryPerformance[item.category]!['itemCount'] =
          (categoryPerformance[item.category]!['itemCount'] as int) + 1;
    }

    // Aggregate order data
    for (final order in orders) {
      if (order.status == OrderStatus.completed ||
          order.status == OrderStatus.delivered) {
        for (final item in order.items) {
          final itemName = item['name'] as String;
          final quantity = item['quantity'] as int;
          final price = (item['price'] as num).toDouble();
          final revenue = quantity * price;

          if (itemPerformance.containsKey(itemName)) {
            itemPerformance[itemName]!['ordersCount'] =
                (itemPerformance[itemName]!['ordersCount'] as int) + 1;
            itemPerformance[itemName]!['totalQuantity'] =
                (itemPerformance[itemName]!['totalQuantity'] as int) + quantity;
            itemPerformance[itemName]!['totalRevenue'] =
                (itemPerformance[itemName]!['totalRevenue'] as double) +
                revenue;

            final category = itemPerformance[itemName]!['category'] as String;
            if (categoryPerformance.containsKey(category)) {
              categoryPerformance[category]!['ordersCount'] =
                  (categoryPerformance[category]!['ordersCount'] as int) + 1;
              categoryPerformance[category]!['totalRevenue'] =
                  (categoryPerformance[category]!['totalRevenue'] as double) +
                  revenue;
            }
          }
        }
      }
    }

    return {
      'itemPerformance': itemPerformance,
      'categoryPerformance': categoryPerformance,
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }
}

// Riverpod providers
final analyticsServiceProvider = Provider<AnalyticsService>((ref) {
  return AnalyticsService();
});

// Dashboard analytics provider
final dashboardAnalyticsProvider =
    FutureProvider.family<Map<String, dynamic>, Map<String, DateTime?>>((
      ref,
      dateRange,
    ) {
      return ref
          .read(analyticsServiceProvider)
          .getDashboardAnalytics(
            startDate: dateRange['start'],
            endDate: dateRange['end'],
          );
    });

// Real-time metrics provider
final realTimeMetricsProvider = FutureProvider<Map<String, dynamic>>((ref) {
  return ref.read(analyticsServiceProvider).getRealTimeMetrics();
});

// Sales analytics provider
final salesAnalyticsProvider =
    FutureProvider.family<Map<String, dynamic>, Map<String, dynamic>>((
      ref,
      params,
    ) {
      return ref
          .read(analyticsServiceProvider)
          .getSalesAnalytics(
            startDate: params['startDate'] as DateTime?,
            endDate: params['endDate'] as DateTime?,
            period: params['period'] as String? ?? 'daily',
          );
    });

// Popular items provider
final popularItemsProvider =
    FutureProvider.family<Map<String, dynamic>, Map<String, dynamic>>((
      ref,
      params,
    ) {
      return ref
          .read(analyticsServiceProvider)
          .getPopularItemsAnalytics(
            startDate: params['startDate'] as DateTime?,
            endDate: params['endDate'] as DateTime?,
            limit: params['limit'] as int? ?? 10,
          );
    });

// Customer analytics provider
final customerAnalyticsProvider =
    FutureProvider.family<Map<String, dynamic>, Map<String, DateTime?>>((
      ref,
      dateRange,
    ) {
      return ref
          .read(analyticsServiceProvider)
          .getCustomerAnalytics(
            startDate: dateRange['start'],
            endDate: dateRange['end'],
          );
    });

// Operational analytics provider
final operationalAnalyticsProvider =
    FutureProvider.family<Map<String, dynamic>, Map<String, DateTime?>>((
      ref,
      dateRange,
    ) {
      return ref
          .read(analyticsServiceProvider)
          .getOperationalAnalytics(
            startDate: dateRange['start'],
            endDate: dateRange['end'],
          );
    });

// Mock data helper methods
extension AnalyticsServiceMocks on AnalyticsService {
  Map<String, double> _generateMockDailyRevenue(DateTime start, DateTime end) {
    final dailyRevenue = <String, double>{};
    final days = end.difference(start).inDays;

    for (int i = 0; i <= days; i++) {
      final date = start.add(Duration(days: i));
      final dateKey =
          '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      // Generate random revenue between 50,000 and 200,000 TZS
      final revenue = 50000 + (i * 3000) + ((i % 7) * 15000);
      dailyRevenue[dateKey] = revenue.toDouble();
    }

    return dailyRevenue;
  }

  Map<int, int> _generateMockHourlyOrders() {
    return {
      8: 2,
      9: 5,
      10: 8,
      11: 12,
      12: 25,
      13: 30,
      14: 18,
      15: 10,
      16: 8,
      17: 15,
      18: 22,
      19: 35,
      20: 28,
      21: 12,
      22: 5,
    };
  }

  Map<String, dynamic> _getMockMenuPerformance() {
    return {
      'topPerformers': [
        {
          'name': 'Choco Croissant',
          'category': 'Dessert',
          'revenue': 234000.0,
          'orders': 45,
        },
        {
          'name': 'Beef Burger',
          'category': 'Main Course',
          'revenue': 456000.0,
          'orders': 38,
        },
        {
          'name': 'Coffee Latte',
          'category': 'Beverage',
          'revenue': 123000.0,
          'orders': 67,
        },
      ],
      'categoryPerformance': {
        'Main Course': {'revenue': 1234000.0, 'orders': 156},
        'Dessert': {'revenue': 567000.0, 'orders': 89},
        'Beverage': {'revenue': 345000.0, 'orders': 234},
      },
    };
  }

  Map<String, dynamic> _getMockSalesAnalytics(String period) {
    return {
      'summary': {
        'totalRevenue': 4567000.0,
        'totalOrders': 342,
        'averageOrderValue': 13350.0,
        'period': period,
      },
      'timeSeries': _generateMockTimeSeries(period),
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }

  Map<String, Map<String, dynamic>> _generateMockTimeSeries(String period) {
    final now = DateTime.now();
    final timeSeries = <String, Map<String, dynamic>>{};

    for (int i = 0; i < 30; i++) {
      final date = now.subtract(Duration(days: i));
      String key;

      switch (period) {
        case 'daily':
          key =
              '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
          break;
        case 'weekly':
          key =
              '${date.year}-W${((date.difference(DateTime(date.year, 1, 1)).inDays) / 7).ceil()}';
          break;
        case 'monthly':
          key = '${date.year}-${date.month.toString().padLeft(2, '0')}';
          break;
        default:
          key = date.toIso8601String().split('T')[0];
      }

      final revenue = 50000 + (i * 2000) + ((i % 7) * 10000);
      final orders = 5 + (i % 15);

      timeSeries[key] = {
        'revenue': revenue.toDouble(),
        'orders': orders,
        'averageOrderValue': orders > 0 ? revenue / orders : 0.0,
      };
    }

    return timeSeries;
  }

  Map<String, dynamic> _getMockPopularItemsAnalytics(int limit) {
    final mockItems = [
      {
        'name': 'Choco Croissant',
        'totalQuantity': 89,
        'totalRevenue': 234000.0,
        'orderCount': 45,
        'averageQuantityPerOrder': 1.98,
      },
      {
        'name': 'Beef Burger',
        'totalQuantity': 76,
        'totalRevenue': 456000.0,
        'orderCount': 38,
        'averageQuantityPerOrder': 2.0,
      },
      {
        'name': 'Coffee Latte',
        'totalQuantity': 134,
        'totalRevenue': 123000.0,
        'orderCount': 67,
        'averageQuantityPerOrder': 2.0,
      },
      {
        'name': 'Chicken Pizza',
        'totalQuantity': 65,
        'totalRevenue': 345000.0,
        'orderCount': 32,
        'averageQuantityPerOrder': 2.03,
      },
      {
        'name': 'Caesar Salad',
        'totalQuantity': 45,
        'totalRevenue': 178000.0,
        'orderCount': 28,
        'averageQuantityPerOrder': 1.61,
      },
    ];

    return {
      'topItems': mockItems.take(limit).toList(),
      'totalUniqueItems': mockItems.length,
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }

  Map<String, dynamic> _getMockCustomerAnalytics() {
    return {
      'summary': {
        'totalCustomers': 156,
        'newCustomers': 23,
        'returningCustomers': 89,
        'customerRetentionRate': 57.1,
      },
      'topCustomers': [
        {
          'email': '<EMAIL>',
          'name': 'John Doe',
          'totalOrders': 12,
          'totalSpent': 234000.0,
          'averageOrderValue': 19500.0,
        },
        {
          'email': '<EMAIL>',
          'name': 'Jane Smith',
          'totalOrders': 8,
          'totalSpent': 156000.0,
          'averageOrderValue': 19500.0,
        },
        {
          'email': '<EMAIL>',
          'name': 'Bob Johnson',
          'totalOrders': 6,
          'totalSpent': 123000.0,
          'averageOrderValue': 20500.0,
        },
      ],
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }

  Map<String, dynamic> _getMockOperationalAnalytics() {
    return {
      'processingTimes': {
        'pending-confirmed': 5.2,
        'confirmed-preparing': 8.5,
        'preparing-ready': 15.3,
        'ready-outForDelivery': 3.1,
        'outForDelivery-delivered': 25.7,
      },
      'peakHours': [12, 19, 13],
      'hourlyDistribution': {
        8: 2,
        9: 5,
        10: 8,
        11: 12,
        12: 25,
        13: 30,
        14: 18,
        15: 10,
        16: 8,
        17: 15,
        18: 22,
        19: 35,
        20: 28,
        21: 12,
        22: 5,
      },
      'dayOfWeekDistribution': {
        1: 45,
        2: 52,
        3: 48,
        4: 56,
        5: 78,
        6: 89,
        7: 67,
      },
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }
}
