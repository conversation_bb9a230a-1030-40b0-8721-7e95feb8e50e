import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/order.dart';

class AdminOrderService {
  final FirebaseFirestore _db = FirebaseFirestore.instance;

  // Get real-time stream of all orders
  Stream<List<OrderModel>> getAllOrdersStream() {
    return _db
        .collection('orders')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => OrderModel.fromMap(doc.data()))
              .toList();
        });
  }

  // Get orders by status
  Stream<List<OrderModel>> getOrdersByStatusStream(OrderStatus status) {
    return _db
        .collection('orders')
        .where('status', isEqualTo: status.name)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => OrderModel.fromMap(doc.data()))
              .toList();
        });
  }

  // Get pending orders (most important for staff)
  Stream<List<OrderModel>> getPendingOrdersStream() {
    return getOrdersByStatusStream(OrderStatus.pending);
  }

  // Get orders for today
  Stream<List<OrderModel>> getTodayOrdersStream() {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    return _db
        .collection('orders')
        .where(
          'createdAt',
          isGreaterThanOrEqualTo: startOfDay.toIso8601String(),
        )
        .where('createdAt', isLessThan: endOfDay.toIso8601String())
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => OrderModel.fromMap(doc.data()))
              .toList();
        });
  }

  // Update order status with staff tracking
  Future<void> updateOrderStatus({
    required String orderId,
    required OrderStatus newStatus,
    required String staffId,
    String? note,
  }) async {
    try {
      // Get current order
      final orderDoc = await _db.collection('orders').doc(orderId).get();
      if (!orderDoc.exists) {
        throw Exception('Order not found');
      }

      final order = OrderModel.fromMap(orderDoc.data()!);

      // Create status history entry
      final statusHistoryEntry = {
        'status': newStatus.name,
        'timestamp': DateTime.now().toIso8601String(),
        'note': note ?? 'Status updated by staff',
        'staffId': staffId,
      };

      final updatedStatusHistory = List<Map<String, dynamic>>.from(
        order.statusHistory ?? [],
      );
      updatedStatusHistory.add(statusHistoryEntry);

      // Update order
      await _db.collection('orders').doc(orderId).update({
        'status': newStatus.name,
        'updatedAt': DateTime.now().toIso8601String(),
        'statusHistory': updatedStatusHistory,
        'assignedStaff': staffId,
      });

      // Log activity
      await _logStaffActivity(
        staffId: staffId,
        action: 'update_order_status',
        details: {
          'orderId': orderId,
          'oldStatus': order.status.name,
          'newStatus': newStatus.name,
          'note': note,
        },
      );
    } catch (e) {
      throw Exception('Failed to update order status: $e');
    }
  }

  // Bulk update multiple orders
  Future<void> bulkUpdateOrderStatus({
    required List<String> orderIds,
    required OrderStatus newStatus,
    required String staffId,
    String? note,
  }) async {
    final batch = _db.batch();

    try {
      for (final orderId in orderIds) {
        final orderRef = _db.collection('orders').doc(orderId);

        // Get current order for status history
        final orderDoc = await orderRef.get();
        if (orderDoc.exists) {
          final order = OrderModel.fromMap(orderDoc.data()!);

          final statusHistoryEntry = {
            'status': newStatus.name,
            'timestamp': DateTime.now().toIso8601String(),
            'note': note ?? 'Bulk status update by staff',
            'staffId': staffId,
          };

          final updatedStatusHistory = List<Map<String, dynamic>>.from(
            order.statusHistory ?? [],
          );
          updatedStatusHistory.add(statusHistoryEntry);

          batch.update(orderRef, {
            'status': newStatus.name,
            'updatedAt': DateTime.now().toIso8601String(),
            'statusHistory': updatedStatusHistory,
            'assignedStaff': staffId,
          });
        }
      }

      await batch.commit();

      // Log bulk activity
      await _logStaffActivity(
        staffId: staffId,
        action: 'bulk_update_orders',
        details: {
          'orderIds': orderIds,
          'newStatus': newStatus.name,
          'count': orderIds.length,
          'note': note,
        },
      );
    } catch (e) {
      throw Exception('Failed to bulk update orders: $e');
    }
  }

  // Search orders
  Future<List<OrderModel>> searchOrders({
    String? query,
    OrderStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 50,
  }) async {
    try {
      Query ordersQuery = _db.collection('orders');

      // Filter by status
      if (status != null) {
        ordersQuery = ordersQuery.where('status', isEqualTo: status.name);
      }

      // Filter by date range
      if (startDate != null) {
        ordersQuery = ordersQuery.where(
          'createdAt',
          isGreaterThanOrEqualTo: startDate.toIso8601String(),
        );
      }
      if (endDate != null) {
        ordersQuery = ordersQuery.where(
          'createdAt',
          isLessThanOrEqualTo: endDate.toIso8601String(),
        );
      }

      ordersQuery = ordersQuery
          .orderBy('createdAt', descending: true)
          .limit(limit);

      final snapshot = await ordersQuery.get();
      List<OrderModel> orders =
          snapshot.docs
              .map(
                (doc) => OrderModel.fromMap(doc.data() as Map<String, dynamic>),
              )
              .toList();

      // Filter by query (order ID, customer name, etc.)
      if (query != null && query.isNotEmpty) {
        orders =
            orders.where((order) {
              return order.id.toLowerCase().contains(query.toLowerCase()) ||
                  (order.customerInfo?['name']?.toLowerCase().contains(
                        query.toLowerCase(),
                      ) ??
                      false) ||
                  (order.customerInfo?['email']?.toLowerCase().contains(
                        query.toLowerCase(),
                      ) ??
                      false);
            }).toList();
      }

      return orders;
    } catch (e) {
      throw Exception('Failed to search orders: $e');
    }
  }

  // Get order statistics
  Future<Map<String, dynamic>> getOrderStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final start =
          startDate ?? DateTime.now().subtract(const Duration(days: 30));
      final end = endDate ?? DateTime.now();

      final snapshot =
          await _db
              .collection('orders')
              .where(
                'createdAt',
                isGreaterThanOrEqualTo: start.toIso8601String(),
              )
              .where('createdAt', isLessThanOrEqualTo: end.toIso8601String())
              .get();

      final orders =
          snapshot.docs.map((doc) => OrderModel.fromMap(doc.data())).toList();

      // Calculate statistics
      final totalOrders = orders.length;
      final totalRevenue = orders.fold<double>(
        0,
        (total, order) => total + order.total,
      );
      final averageOrderValue =
          totalOrders > 0 ? totalRevenue / totalOrders : 0.0;

      // Count by status
      final statusCounts = <String, int>{};
      for (final status in OrderStatus.values) {
        statusCounts[status.name] =
            orders.where((o) => o.status == status).length;
      }

      // Today's statistics
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final todayOrders =
          orders.where((order) => order.createdAt.isAfter(startOfDay)).toList();

      return {
        'totalOrders': totalOrders,
        'totalRevenue': totalRevenue,
        'averageOrderValue': averageOrderValue,
        'statusCounts': statusCounts,
        'todayOrders': todayOrders.length,
        'todayRevenue': todayOrders.fold<double>(
          0,
          (total, order) => total + order.total,
        ),
        'pendingOrders': statusCounts[OrderStatus.pending.name] ?? 0,
        'completedOrders': statusCounts[OrderStatus.completed.name] ?? 0,
      };
    } catch (e) {
      throw Exception('Failed to get order statistics: $e');
    }
  }

  // Assign order to staff
  Future<void> assignOrderToStaff({
    required String orderId,
    required String staffId,
    String? note,
  }) async {
    try {
      // Get staff info for better tracking
      final staffDoc = await _db.collection('users').doc(staffId).get();
      final staffData = staffDoc.data();
      final staffName =
          staffDoc.exists && staffData != null
              ? staffData['name'] ?? 'Unknown Staff'
              : 'Unknown Staff';

      await _db.collection('orders').doc(orderId).update({
        'assignedStaff': staffName,
        'assignedStaffId': staffId,
        'assignedAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      });

      await _logStaffActivity(
        staffId: staffId,
        action: 'assign_order',
        details: {'orderId': orderId, 'note': note, 'staffName': staffName},
      );
    } catch (e) {
      throw Exception('Failed to assign order: $e');
    }
  }

  // Bulk assign orders to staff member
  Future<void> bulkAssignOrdersToStaff({
    required List<String> orderIds,
    required String staffId,
    String? note,
  }) async {
    try {
      // Get staff info
      final staffDoc = await _db.collection('users').doc(staffId).get();
      final staffData = staffDoc.data();
      final staffName =
          staffDoc.exists && staffData != null
              ? staffData['name'] ?? 'Unknown Staff'
              : 'Unknown Staff';

      final batch = _db.batch();
      final timestamp = DateTime.now().toIso8601String();

      for (final orderId in orderIds) {
        final orderRef = _db.collection('orders').doc(orderId);
        batch.update(orderRef, {
          'assignedStaff': staffName,
          'assignedStaffId': staffId,
          'assignedAt': timestamp,
          'updatedAt': timestamp,
        });
      }

      await batch.commit();

      // Log bulk assignment
      await _logStaffActivity(
        staffId: staffId,
        action: 'bulk_assign_orders',
        details: {
          'orderIds': orderIds,
          'orderCount': orderIds.length,
          'note': note,
          'staffName': staffName,
        },
      );
    } catch (e) {
      throw Exception('Failed to bulk assign orders to staff: $e');
    }
  }

  // Unassign order from staff
  Future<void> unassignOrderFromStaff({
    required String orderId,
    String? note,
  }) async {
    try {
      await _db.collection('orders').doc(orderId).update({
        'assignedStaff': FieldValue.delete(),
        'assignedStaffId': FieldValue.delete(),
        'assignedAt': FieldValue.delete(),
        'updatedAt': DateTime.now().toIso8601String(),
      });

      await _logStaffActivity(
        staffId: 'system',
        action: 'unassign_order',
        details: {'orderId': orderId, 'note': note},
      );
    } catch (e) {
      throw Exception('Failed to unassign order from staff: $e');
    }
  }

  // Get orders assigned to specific staff member
  Stream<List<OrderModel>> getOrdersAssignedToStaff(String staffId) {
    return _db
        .collection('orders')
        .where('assignedStaffId', isEqualTo: staffId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs.map((doc) {
            final data = doc.data();
            data['id'] = doc.id;
            return OrderModel.fromMap(data);
          }).toList();
        });
  }

  // Get staff workload (number of assigned orders by status)
  Future<Map<String, Map<String, int>>> getStaffWorkload() async {
    try {
      final ordersSnapshot =
          await _db
              .collection('orders')
              .where('assignedStaffId', isNotEqualTo: null)
              .get();

      final workload = <String, Map<String, int>>{};

      for (final doc in ordersSnapshot.docs) {
        final data = doc.data();
        final staffId = data['assignedStaffId'] as String?;
        final status = data['status'] as String?;

        if (staffId != null && status != null) {
          workload[staffId] ??= {};
          workload[staffId]![status] = (workload[staffId]![status] ?? 0) + 1;
        }
      }

      return workload;
    } catch (e) {
      throw Exception('Failed to get staff workload: $e');
    }
  }

  // Log staff activity
  Future<void> _logStaffActivity({
    required String staffId,
    required String action,
    required Map<String, dynamic> details,
  }) async {
    try {
      await _db.collection('staff_activities').add({
        'staffId': staffId,
        'action': action,
        'details': details,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      // Log activity failure shouldn't break the main operation
      // Failed to log staff activity: $e
    }
  }

  // Get staff activities
  Stream<List<Map<String, dynamic>>> getStaffActivitiesStream({
    String? staffId,
    int limit = 50,
  }) {
    Query query = _db.collection('staff_activities');

    if (staffId != null) {
      query = query.where('staffId', isEqualTo: staffId);
    }

    return query
        .orderBy('timestamp', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map(
                (doc) => {'id': doc.id, ...doc.data() as Map<String, dynamic>},
              )
              .toList();
        });
  }
}

// Riverpod providers
final adminOrderServiceProvider = Provider<AdminOrderService>((ref) {
  return AdminOrderService();
});

// Stream providers for real-time data
final allOrdersStreamProvider = StreamProvider<List<OrderModel>>((ref) {
  return ref.read(adminOrderServiceProvider).getAllOrdersStream();
});

final pendingOrdersStreamProvider = StreamProvider<List<OrderModel>>((ref) {
  return ref.read(adminOrderServiceProvider).getPendingOrdersStream();
});

final todayOrdersStreamProvider = StreamProvider<List<OrderModel>>((ref) {
  return ref.read(adminOrderServiceProvider).getTodayOrdersStream();
});

final orderStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) {
  return ref.read(adminOrderServiceProvider).getOrderStatistics();
});
