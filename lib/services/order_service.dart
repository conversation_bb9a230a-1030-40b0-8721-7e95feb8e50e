import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/order.dart';
import 'crashlytics_service.dart';
import 'performance_service.dart';

class OrderService {
  final Ref _ref;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  OrderService(this._ref);

  // Create a new order
  Future<String> createOrder(OrderModel order) async {
    return await _ref.read(performanceServiceProvider).trackOrderOperation(
      'create_order',
      () async {
        try {
          await _ref.read(crashlyticsServiceProvider).recordOrderEvent(
            'order_creation_started',
            orderId: order.id,
            total: order.total,
          );

          await _firestore.collection('orders').doc(order.id).set(order.toMap());

          await _ref.read(crashlyticsServiceProvider).recordOrderEvent(
            'order_created_successfully',
            orderId: order.id,
            total: order.total,
          );

          return order.id;
        } catch (e) {
          await _ref.read(crashlyticsServiceProvider).recordOrderEvent(
            'order_creation_failed',
            orderId: order.id,
            total: order.total,
            error: e.toString(),
          );
          rethrow;
        }
      },
    );
  }

  // Update order status
  Future<void> updateOrderStatus({
    required String orderId,
    required OrderStatus newStatus,
    String? note,
    DateTime? estimatedDeliveryTime,
  }) async {
    return await _ref.read(performanceServiceProvider).trackOrderOperation(
      'update_order_status',
      () async {
        try {
          await _ref.read(crashlyticsServiceProvider).recordOrderEvent(
            'order_status_update_started',
            orderId: orderId,
          );

          final orderRef = _firestore.collection('orders').doc(orderId);
          final orderDoc = await orderRef.get();

          if (!orderDoc.exists) {
            throw Exception('Order not found');
          }

          final order = OrderModel.fromMap(orderDoc.data()!);
          
          // Create status history entry
          final statusHistoryEntry = {
            'status': newStatus.name,
            'timestamp': DateTime.now().toIso8601String(),
            'note': note,
          };

          final updatedStatusHistory = List<Map<String, dynamic>>.from(
            order.statusHistory ?? [],
          );
          updatedStatusHistory.add(statusHistoryEntry);

          // Update order
          final updatedOrder = order.copyWith(
            status: newStatus,
            updatedAt: DateTime.now(),
            estimatedDeliveryTime: estimatedDeliveryTime,
            statusHistory: updatedStatusHistory,
          );

          await orderRef.update(updatedOrder.toMap());

          await _ref.read(crashlyticsServiceProvider).recordOrderEvent(
            'order_status_updated',
            orderId: orderId,
          );
        } catch (e) {
          await _ref.read(crashlyticsServiceProvider).recordOrderEvent(
            'order_status_update_failed',
            orderId: orderId,
            error: e.toString(),
          );
          rethrow;
        }
      },
    );
  }

  // Get user orders with real-time updates
  Stream<List<OrderModel>> getUserOrdersStream(String userId) {
    return _firestore
        .collection('orders')
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return OrderModel.fromMap(doc.data());
      }).toList();
    });
  }

  // Get specific order with real-time updates
  Stream<OrderModel?> getOrderStream(String orderId) {
    return _firestore
        .collection('orders')
        .doc(orderId)
        .snapshots()
        .map((snapshot) {
      if (snapshot.exists) {
        return OrderModel.fromMap(snapshot.data()!);
      }
      return null;
    });
  }

  // Get user orders (one-time fetch)
  Future<List<OrderModel>> getUserOrders(String userId) async {
    return await _ref.read(performanceServiceProvider).trackOrderOperation(
      'get_user_orders',
      () async {
        try {
          final snapshot = await _firestore
              .collection('orders')
              .where('userId', isEqualTo: userId)
              .orderBy('createdAt', descending: true)
              .get();

          return snapshot.docs.map((doc) {
            return OrderModel.fromMap(doc.data());
          }).toList();
        } catch (e) {
          await _ref.read(crashlyticsServiceProvider).recordOrderEvent(
            'get_user_orders_failed',
            error: e.toString(),
          );
          rethrow;
        }
      },
    );
  }

  // Cancel order
  Future<void> cancelOrder(String orderId, {String? reason}) async {
    return await _ref.read(performanceServiceProvider).trackOrderOperation(
      'cancel_order',
      () async {
        try {
          await updateOrderStatus(
            orderId: orderId,
            newStatus: OrderStatus.cancelled,
            note: reason ?? 'Order cancelled by customer',
          );

          await _ref.read(crashlyticsServiceProvider).recordOrderEvent(
            'order_cancelled',
            orderId: orderId,
          );
        } catch (e) {
          await _ref.read(crashlyticsServiceProvider).recordOrderEvent(
            'order_cancellation_failed',
            orderId: orderId,
            error: e.toString(),
          );
          rethrow;
        }
      },
    );
  }

  // Get order by ID
  Future<OrderModel?> getOrder(String orderId) async {
    return await _ref.read(performanceServiceProvider).trackOrderOperation(
      'get_order',
      () async {
        try {
          final doc = await _firestore.collection('orders').doc(orderId).get();
          
          if (doc.exists) {
            return OrderModel.fromMap(doc.data()!);
          }
          return null;
        } catch (e) {
          await _ref.read(crashlyticsServiceProvider).recordOrderEvent(
            'get_order_failed',
            orderId: orderId,
            error: e.toString(),
          );
          rethrow;
        }
      },
    );
  }

  // Get active orders for a user
  Future<List<OrderModel>> getActiveOrders(String userId) async {
    return await _ref.read(performanceServiceProvider).trackOrderOperation(
      'get_active_orders',
      () async {
        try {
          final snapshot = await _firestore
              .collection('orders')
              .where('userId', isEqualTo: userId)
              .where('status', whereIn: [
                OrderStatus.pending.name,
                OrderStatus.confirmed.name,
                OrderStatus.preparing.name,
                OrderStatus.ready.name,
                OrderStatus.outForDelivery.name,
              ])
              .orderBy('createdAt', descending: true)
              .get();

          return snapshot.docs.map((doc) {
            return OrderModel.fromMap(doc.data());
          }).toList();
        } catch (e) {
          await _ref.read(crashlyticsServiceProvider).recordOrderEvent(
            'get_active_orders_failed',
            error: e.toString(),
          );
          rethrow;
        }
      },
    );
  }

  // Get order history for a user
  Future<List<OrderModel>> getOrderHistory(String userId, {int limit = 20}) async {
    return await _ref.read(performanceServiceProvider).trackOrderOperation(
      'get_order_history',
      () async {
        try {
          final snapshot = await _firestore
              .collection('orders')
              .where('userId', isEqualTo: userId)
              .where('status', whereIn: [
                OrderStatus.completed.name,
                OrderStatus.delivered.name,
                OrderStatus.cancelled.name,
              ])
              .orderBy('createdAt', descending: true)
              .limit(limit)
              .get();

          return snapshot.docs.map((doc) {
            return OrderModel.fromMap(doc.data());
          }).toList();
        } catch (e) {
          await _ref.read(crashlyticsServiceProvider).recordOrderEvent(
            'get_order_history_failed',
            error: e.toString(),
          );
          rethrow;
        }
      },
    );
  }

  // Estimate delivery time based on order type and current load
  DateTime estimateDeliveryTime(OrderType orderType) {
    final now = DateTime.now();
    
    switch (orderType) {
      case OrderType.pickup:
        return now.add(const Duration(minutes: 15)); // 15 minutes for pickup
      case OrderType.delivery:
        return now.add(const Duration(minutes: 45)); // 45 minutes for delivery
      case OrderType.dineIn:
        return now.add(const Duration(minutes: 20)); // 20 minutes for dine-in
    }
  }

  // Add payment information to order
  Future<void> addPaymentInfo({
    required String orderId,
    required String paymentIntentId,
    required String paymentMethod,
  }) async {
    return await _ref.read(performanceServiceProvider).trackOrderOperation(
      'add_payment_info',
      () async {
        try {
          await _firestore.collection('orders').doc(orderId).update({
            'paymentIntentId': paymentIntentId,
            'paymentMethod': paymentMethod,
            'updatedAt': DateTime.now().toIso8601String(),
          });

          await _ref.read(crashlyticsServiceProvider).recordOrderEvent(
            'payment_info_added',
            orderId: orderId,
          );
        } catch (e) {
          await _ref.read(crashlyticsServiceProvider).recordOrderEvent(
            'add_payment_info_failed',
            orderId: orderId,
            error: e.toString(),
          );
          rethrow;
        }
      },
    );
  }

  // Get order statistics for user
  Future<Map<String, dynamic>> getOrderStatistics(String userId) async {
    return await _ref.read(performanceServiceProvider).trackOrderOperation(
      'get_order_statistics',
      () async {
        try {
          final snapshot = await _firestore
              .collection('orders')
              .where('userId', isEqualTo: userId)
              .get();

          final orders = snapshot.docs.map((doc) => OrderModel.fromMap(doc.data())).toList();
          
          final totalOrders = orders.length;
          final completedOrders = orders.where((o) => o.status == OrderStatus.completed || o.status == OrderStatus.delivered).length;
          final cancelledOrders = orders.where((o) => o.status == OrderStatus.cancelled).length;
          final totalSpent = orders.fold<double>(0.0, (sum, order) => sum + order.total);
          final averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0.0;

          return {
            'totalOrders': totalOrders,
            'completedOrders': completedOrders,
            'cancelledOrders': cancelledOrders,
            'totalSpent': totalSpent,
            'averageOrderValue': averageOrderValue,
          };
        } catch (e) {
          await _ref.read(crashlyticsServiceProvider).recordOrderEvent(
            'get_order_statistics_failed',
            error: e.toString(),
          );
          rethrow;
        }
      },
    );
  }
}

// Riverpod provider for OrderService
final orderServiceProvider = Provider<OrderService>((ref) {
  return OrderService(ref);
});
