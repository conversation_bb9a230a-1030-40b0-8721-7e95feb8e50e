import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user.dart';

class PermissionService {
  // Default permissions for each role
  static const Map<UserRole, List<Permission>> defaultPermissions = {
    UserRole.customer: [],
    UserRole.staff: [
      Permission.viewOrders,
      Permission.updateOrderStatus,
      Permission.viewMenu,
    ],
    UserRole.manager: [
      Permission.viewOrders,
      Permission.updateOrderStatus,
      Permission.cancelOrders,
      Permission.viewMenu,
      Permission.editMenu,
      Permission.addMenuItems,
      Permission.deleteMenuItems,
      Permission.viewAnalytics,
      Permission.viewUsers,
    ],
    UserRole.admin: [
      // Admin has all permissions
      Permission.viewOrders,
      Permission.updateOrderStatus,
      Permission.cancelOrders,
      Permission.viewMenu,
      Permission.editMenu,
      Permission.addMenuItems,
      Permission.deleteMenuItems,
      Permission.viewUsers,
      Permission.editUsers,
      Permission.deleteUsers,
      Permission.viewAnalytics,
      Permission.exportReports,
      Permission.editRestaurantSettings,
      Permission.manageStaff,
    ],
  };

  // Get default permissions for a role
  List<Permission> getDefaultPermissions(UserRole role) {
    return defaultPermissions[role] ?? [];
  }

  // Check if user has permission
  bool hasPermission(AppUser? user, Permission permission) {
    if (user == null) return false;
    return user.hasPermission(permission);
  }

  // Check if user has any of the permissions
  bool hasAnyPermission(AppUser? user, List<Permission> permissions) {
    if (user == null) return false;
    return permissions.any((permission) => user.hasPermission(permission));
  }

  // Check if user has all permissions
  bool hasAllPermissions(AppUser? user, List<Permission> permissions) {
    if (user == null) return false;
    return permissions.every((permission) => user.hasPermission(permission));
  }

  // Check if user can access admin features
  bool canAccessAdmin(AppUser? user) {
    if (user == null) return false;
    return user.isAdminUser;
  }

  // Check if user can manage orders
  bool canManageOrders(AppUser? user) {
    return hasAnyPermission(user, [
      Permission.viewOrders,
      Permission.updateOrderStatus,
      Permission.cancelOrders,
    ]);
  }

  // Check if user can manage menu
  bool canManageMenu(AppUser? user) {
    return hasAnyPermission(user, [
      Permission.viewMenu,
      Permission.editMenu,
      Permission.addMenuItems,
      Permission.deleteMenuItems,
    ]);
  }

  // Check if user can view analytics
  bool canViewAnalytics(AppUser? user) {
    return hasPermission(user, Permission.viewAnalytics);
  }

  // Check if user can manage settings
  bool canManageSettings(AppUser? user) {
    return hasAnyPermission(user, [
      Permission.editRestaurantSettings,
      Permission.manageStaff,
    ]);
  }

  // Create user with role and default permissions
  AppUser createUserWithRole({
    required String uid,
    required String email,
    required String name,
    required UserRole role,
    String? restaurantId,
    List<String>? preferences,
  }) {
    final defaultPerms = getDefaultPermissions(role);
    final now = DateTime.now();

    return AppUser(
      uid: uid,
      email: email,
      name: name,
      role: role,
      permissions: defaultPerms,
      restaurantId: restaurantId,
      preferences: preferences ?? [],
      isActive: true,
      createdAt: now,
      updatedAt: now,
    );
  }
}

// Riverpod provider for PermissionService
final permissionServiceProvider = Provider<PermissionService>((ref) {
  return PermissionService();
});
