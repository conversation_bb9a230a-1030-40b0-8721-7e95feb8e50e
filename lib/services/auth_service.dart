import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:smart_restaurant_menu/models/order.dart';
import '../models/user.dart';
import 'database_service.dart';
import 'crashlytics_service.dart';
import 'performance_service.dart';
import 'permission_service.dart';
import 'secure_storage_service.dart';

// Define the AuthState
class AuthState {
  final AppUser? user;
  final String? error;
  final bool isEmailVerified;
  final bool isLoading;

  AuthState({
    this.user,
    this.error,
    this.isEmailVerified = false,
    this.isLoading = false,
  });

  AuthState copyWith({
    AppUser? user,
    String? error,
    bool? isEmailVerified,
    bool? isLoading,
  }) {
    return AuthState(
      user: user ?? this.user,
      error: error,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

// StateNotifier for AuthService
class AuthService extends StateNotifier<AuthState> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final Ref _ref;

  AuthService(this._ref) : super(AuthState(isLoading: true)) {
    // Initialize with current user if available
    _initializeAuth();

    _auth.authStateChanges().listen((User? user) {
      if (user != null) {
        _ref
            .read(crashlyticsServiceProvider)
            .recordAuthEvent('auth_state_changed', userId: user.uid);
        _ref
            .read(databaseServiceProvider)
            .getUser(user.uid)
            .then((appUser) {
              if (appUser != null) {
                print(
                  'Auth: User data loaded from DB - Email: ${appUser.email}, Role: ${appUser.role.name}',
                );
                state = AuthState(
                  user: appUser,
                  isEmailVerified: user.emailVerified,
                  isLoading: false,
                );
              } else {
                print(
                  'Auth: User document not found in database for UID: ${user.uid}',
                );
                state = AuthState(
                  user: null,
                  isEmailVerified: user.emailVerified,
                  isLoading: false,
                );
              }
            })
            .catchError((e) {
              // If database fetch fails, still set user as authenticated
              print('Auth: Failed to load user data from DB: $e');
              state = AuthState(
                user: null,
                isEmailVerified: user.emailVerified,
                isLoading: false,
              );
            });
      } else {
        _ref
            .read(crashlyticsServiceProvider)
            .recordAuthEvent('user_signed_out');
        state = AuthState(isLoading: false);
      }
    });
  }

  Future<void> _initializeAuth() async {
    final currentUser = _auth.currentUser;
    if (currentUser != null) {
      try {
        // Try to get user data immediately
        final appUser = await _ref
            .read(databaseServiceProvider)
            .getUser(currentUser.uid);
        state = AuthState(
          user: appUser,
          isEmailVerified: currentUser.emailVerified,
          isLoading: false,
        );
      } catch (e) {
        // If database fetch fails, still set user as authenticated but without app user data
        state = AuthState(
          user: null,
          isEmailVerified: currentUser.emailVerified,
          isLoading: false,
        );
      }
    } else {
      // No user signed in
      state = AuthState(isLoading: false);
    }
  }

  Future<void> signIn(String email, String password) async {
    await _ref.read(performanceServiceProvider).trackAuthOperation(
      'sign_in',
      () async {
        try {
          state = state.copyWith(isLoading: true, error: null);

          await _ref
              .read(crashlyticsServiceProvider)
              .recordAuthEvent('sign_in_attempt');

          UserCredential result = await _auth.signInWithEmailAndPassword(
            email: email,
            password: password,
          );

          final user = await _ref
              .read(databaseServiceProvider)
              .getUser(result.user!.uid);

          await _ref
              .read(crashlyticsServiceProvider)
              .recordAuthEvent('sign_in_success', userId: result.user!.uid);

          // Save user data to secure storage
          final secureStorage = _ref.read(secureStorageServiceProvider);
          await secureStorage.saveUserId(result.user!.uid);
          await secureStorage.saveUserEmail(result.user!.email ?? '');
          await secureStorage.saveLastLoginTime(DateTime.now());

          if (result.user!.refreshToken != null) {
            await secureStorage.saveUserToken(result.user!.refreshToken!);
          }

          state = AuthState(
            user: user,
            isEmailVerified: result.user!.emailVerified,
            isLoading: false,
          );
        } catch (e) {
          await _ref
              .read(crashlyticsServiceProvider)
              .recordAuthEvent('sign_in_failed', error: e.toString());
          state = AuthState(error: 'Failed to sign in: $e', isLoading: false);
        }
      },
    );
  }

  Future<void> register(
    String email,
    String password,
    String name, {
    List<String>? preferences,
    UserRole? role,
  }) async {
    await _ref.read(performanceServiceProvider).trackAuthOperation(
      'register',
      () async {
        try {
          state = state.copyWith(isLoading: true, error: null);

          await _ref
              .read(crashlyticsServiceProvider)
              .recordAuthEvent('register_attempt');

          UserCredential result = await _auth.createUserWithEmailAndPassword(
            email: email,
            password: password,
          );

          // Send email verification
          await result.user!.sendEmailVerification();

          // Create user with role and default permissions
          final userRole = role ?? UserRole.customer;
          final user = _ref
              .read(permissionServiceProvider)
              .createUserWithRole(
                uid: result.user!.uid,
                email: email,
                name: name,
                role: userRole,
                preferences: preferences,
              );
          await _ref.read(databaseServiceProvider).saveUser(user);

          await _ref
              .read(crashlyticsServiceProvider)
              .recordAuthEvent('register_success', userId: result.user!.uid);

          // Save user data to secure storage
          final secureStorage = _ref.read(secureStorageServiceProvider);
          await secureStorage.saveUserId(result.user!.uid);
          await secureStorage.saveUserEmail(result.user!.email ?? '');
          await secureStorage.saveLastLoginTime(DateTime.now());

          if (result.user!.refreshToken != null) {
            await secureStorage.saveUserToken(result.user!.refreshToken!);
          }

          state = AuthState(
            user: user,
            isEmailVerified: result.user!.emailVerified,
            isLoading: false,
          );
        } catch (e) {
          await _ref
              .read(crashlyticsServiceProvider)
              .recordAuthEvent('register_failed', error: e.toString());
          state = AuthState(error: 'Failed to register: $e', isLoading: false);
        }
      },
    );
  }

  Future<void> updateUserPreferences(List<String> preferences) async {
    if (state.user != null) {
      final updatedUser = state.user!.copyWith(
        preferences: preferences,
        updatedAt: DateTime.now(),
      );
      await _ref.read(databaseServiceProvider).saveUser(updatedUser);
      state = state.copyWith(user: updatedUser);
    }
  }

  Future<void> updateUserOrderHistory(List<OrderModel> orderHistory) async {
    if (state.user != null) {
      final updatedUser = state.user!.copyWith(
        orderHistory: orderHistory,
        updatedAt: DateTime.now(),
      );
      await _ref.read(databaseServiceProvider).saveUser(updatedUser);
      state = state.copyWith(user: updatedUser);
    }
  }

  User? get currentUser => _auth.currentUser;
  String? get error => state.error;
  bool get isAuthenticated => state.user != null;
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  Future<void> signOut() async {
    await _auth.signOut();

    // Clear secure storage
    await _ref.read(secureStorageServiceProvider).clearUserData();

    state = AuthState();
  }

  Future<void> toggleFavorite(String itemId) async {
    if (state.user == null) return;

    final user = state.user!;
    List<String> updatedFavorites = List.from(user.favorites);
    if (updatedFavorites.contains(itemId)) {
      updatedFavorites.remove(itemId);
    } else {
      updatedFavorites.add(itemId);
    }

    final updatedUser = user.copyWith(
      favorites: updatedFavorites,
      updatedAt: DateTime.now(),
    );

    await _ref.read(databaseServiceProvider).saveUser(updatedUser);
    state = state.copyWith(user: updatedUser);
  }

  Future<void> refreshUser(String uid) async {
    try {
      print('Auth: Refreshing user data for UID: $uid');
      final user = await _ref.read(databaseServiceProvider).getUser(uid);
      if (user != null) {
        print(
          'Auth: User data refreshed successfully - Role: ${user.role.name}',
        );
        state = state.copyWith(
          user: user,
          isEmailVerified: _auth.currentUser?.emailVerified ?? false,
          error: null,
        );
      } else {
        print('Auth: User document not found during refresh for UID: $uid');
        state = state.copyWith(
          user: null,
          error: 'User data not found in database',
        );
      }
    } catch (e) {
      print('Auth: Error refreshing user data: $e');
      state = state.copyWith(error: 'Failed to refresh user: $e');
    }
  }

  // Email verification methods
  Future<void> sendEmailVerification() async {
    try {
      final user = _auth.currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
        state = state.copyWith(error: null);
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to send verification email: $e');
    }
  }

  Future<void> checkEmailVerification() async {
    try {
      await _auth.currentUser?.reload();
      final user = _auth.currentUser;
      if (user != null) {
        state = state.copyWith(isEmailVerified: user.emailVerified);
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to check email verification: $e');
    }
  }

  // Password reset method
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      state = state.copyWith(error: null);
    } catch (e) {
      state = state.copyWith(error: 'Failed to send password reset email: $e');
    }
  }
}

// Riverpod providers
final authServiceProvider = StateNotifierProvider<AuthService, AuthState>((
  ref,
) {
  return AuthService(ref);
});
