class MenuItem {
  final String id;
  final String name;
  final String description;
  final double price;
  final String category;
  final String imageUrl;
  final bool isAvailable;
  final bool isVegetarian;
  final bool isSpicy;
  final bool isPopular;
  final List<String> allergens;
  final List<String> addOns;
  final String weight;
  final int calories;
  final List<String> ingredients;
  final List<String> tags;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  MenuItem({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.category,
    required this.imageUrl,
    this.isAvailable = true,
    this.isVegetarian = false,
    this.isSpicy = false,
    this.isPopular = false,
    this.allergens = const [],
    this.addOns = const [],
    this.weight = '',
    this.calories = 0,
    this.ingredients = const [],
    this.tags = const [],
    this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'category': category,
      'imageUrl': imageUrl,
      'isAvailable': isAvailable,
      'isVegetarian': isVegetarian,
      'isSpicy': isSpicy,
      'isPopular': isPopular,
      'allergens': allergens,
      'addOns': addOns,
      'weight': weight,
      'calories': calories,
      'ingredients': ingredients,
      'tags': tags,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory MenuItem.fromMap(Map<String, dynamic> map, [String? id]) {
    return MenuItem(
      id: id ?? map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      price: (map['price'] ?? 0.0).toDouble(),
      category: map['category'] ?? '',
      imageUrl: map['imageUrl'] ?? '',
      isAvailable: map['isAvailable'] ?? true,
      isVegetarian: map['isVegetarian'] ?? false,
      isSpicy: map['isSpicy'] ?? false,
      isPopular: map['isPopular'] ?? false,
      allergens: List<String>.from(map['allergens'] ?? []),
      addOns: List<String>.from(map['addOns'] ?? []),
      weight: map['weight'] ?? '',
      calories: map['calories'] ?? 0,
      ingredients: List<String>.from(map['ingredients'] ?? []),
      tags: List<String>.from(map['tags'] ?? []),
      createdAt:
          map['createdAt'] != null ? DateTime.parse(map['createdAt']) : null,
      updatedAt:
          map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
    );
  }

  // Helper method to generate tags from boolean fields (for backward compatibility)
  List<String> get generatedTags {
    final tagList = <String>[];
    if (isVegetarian) tagList.add('vegetarian');
    if (isSpicy) tagList.add('spicy');
    if (isPopular) tagList.add('popular');
    tagList.addAll(allergens);
    return tagList;
  }

  // Helper method for backward compatibility with savedWith
  String get savedWith => 'TZS'; // Default currency

  // Copy with method for easy updates
  MenuItem copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    String? category,
    String? imageUrl,
    bool? isAvailable,
    bool? isVegetarian,
    bool? isSpicy,
    bool? isPopular,
    List<String>? allergens,
    List<String>? addOns,
    String? weight,
    int? calories,
    List<String>? ingredients,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MenuItem(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      category: category ?? this.category,
      imageUrl: imageUrl ?? this.imageUrl,
      isAvailable: isAvailable ?? this.isAvailable,
      isVegetarian: isVegetarian ?? this.isVegetarian,
      isSpicy: isSpicy ?? this.isSpicy,
      isPopular: isPopular ?? this.isPopular,
      allergens: allergens ?? this.allergens,
      addOns: addOns ?? this.addOns,
      weight: weight ?? this.weight,
      calories: calories ?? this.calories,
      ingredients: ingredients ?? this.ingredients,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
