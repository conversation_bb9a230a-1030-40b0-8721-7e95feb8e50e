import 'package:smart_restaurant_menu/models/order.dart';

enum UserRole { customer, staff, manager, admin }

enum Permission {
  // Order Management
  viewOrders,
  updateOrderStatus,
  cancelOrders,

  // Menu Management
  viewMenu,
  editMenu,
  addMenuItems,
  deleteMenuItems,

  // User Management
  viewUsers,
  editUsers,
  deleteUsers,

  // Analytics
  viewAnalytics,
  exportReports,

  // Settings
  editRestaurantSettings,
  manageStaff,
}

class AppUser {
  final String uid;
  final String email;
  final String name;
  final UserRole role;
  final List<Permission> permissions;
  final String? restaurantId; // For multi-restaurant support
  final List<String> preferences; // e.g., vegetarian, spicy
  final List<OrderModel> orderHistory; // List of order IDs
  final List<String> favorites; // List of favorite item IDs
  final String? profileImageUrl; // URL to profile image
  final bool isActive; // Whether the user account is active
  final DateTime? createdAt; // When the user was created
  final DateTime? updatedAt; // When the user was last updated

  AppUser({
    required this.uid,
    required this.email,
    required this.name,
    this.role = UserRole.customer,
    this.permissions = const [],
    this.restaurantId,
    this.preferences = const [],
    this.orderHistory = const [],
    this.favorites = const [],
    this.profileImageUrl,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
  });

  // Convenience getter for id (alias for uid)
  String get id => uid;

  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'email': email,
      'name': name,
      'role': role.name,
      'permissions': permissions.map((p) => p.name).toList(),
      'restaurantId': restaurantId,
      'preferences': preferences,
      'orderHistory': orderHistory.map((order) => order.toMap()).toList(),
      'favorites': favorites,
      'profileImageUrl': profileImageUrl,
      'isActive': isActive,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  copyWith({
    String? uid,
    String? email,
    String? name,
    UserRole? role,
    List<Permission>? permissions,
    String? restaurantId,
    List<String>? preferences,
    List<OrderModel>? orderHistory,
    List<String>? favorites,
    String? profileImageUrl,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AppUser(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      name: name ?? this.name,
      role: role ?? this.role,
      permissions: permissions ?? this.permissions,
      restaurantId: restaurantId ?? this.restaurantId,
      preferences: preferences ?? this.preferences,
      orderHistory: orderHistory ?? this.orderHistory,
      favorites: favorites ?? this.favorites,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  factory AppUser.fromMap(Map<String, dynamic> map) {
    return AppUser(
      uid: map['uid'],
      email: map['email'],
      name: map['name'],
      role: _parseUserRole(map['role']),
      permissions: _parsePermissions(map['permissions']),
      restaurantId: map['restaurantId'],
      preferences: List<String>.from(map['preferences'] ?? []),
      orderHistory:
          (map['orderHistory'] as List<dynamic>?)
              ?.map((e) => OrderModel.fromMap(e))
              .toList() ??
          [],
      favorites: List<String>.from(map['favorites'] ?? []),
      profileImageUrl: map['profileImageUrl'],
      isActive: map['isActive'] ?? true,
      createdAt:
          map['createdAt'] != null ? DateTime.parse(map['createdAt']) : null,
      updatedAt:
          map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
    );
  }

  static UserRole _parseUserRole(dynamic role) {
    if (role == null) return UserRole.customer;
    try {
      return UserRole.values.firstWhere((r) => r.name == role);
    } catch (e) {
      return UserRole.customer;
    }
  }

  static List<Permission> _parsePermissions(dynamic permissions) {
    if (permissions == null) return [];
    try {
      return (permissions as List<dynamic>)
          .map((p) => Permission.values.firstWhere((perm) => perm.name == p))
          .toList();
    } catch (e) {
      return [];
    }
  }

  // Helper methods for role checking
  bool get isCustomer => role == UserRole.customer;
  bool get isStaff => role == UserRole.staff;
  bool get isManager => role == UserRole.manager;
  bool get isAdmin => role == UserRole.admin;
  bool get isAdminUser =>
      role == UserRole.staff ||
      role == UserRole.manager ||
      role == UserRole.admin;

  bool hasPermission(Permission permission) {
    return permissions.contains(permission);
  }
}
