class RestaurantSettings {
  final String id;
  final String name;
  final String description;
  final String email;
  final String phone;
  final String address;
  final String city;
  final String country;
  final String timezone;
  final String currency;
  final String logoUrl;
  final Map<String, OperatingHours> operatingHours;
  final PaymentSettings paymentSettings;
  final NotificationSettings notificationSettings;
  final AppSettings appSettings;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  RestaurantSettings({
    required this.id,
    required this.name,
    required this.description,
    required this.email,
    required this.phone,
    required this.address,
    required this.city,
    required this.country,
    this.timezone = 'UTC',
    this.currency = 'TZS',
    this.logoUrl = '',
    this.operatingHours = const {},
    required this.paymentSettings,
    required this.notificationSettings,
    required this.appSettings,
    this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'email': email,
      'phone': phone,
      'address': address,
      'city': city,
      'country': country,
      'timezone': timezone,
      'currency': currency,
      'logoUrl': logoUrl,
      'operatingHours': operatingHours.map((key, value) => MapEntry(key, value.toMap())),
      'paymentSettings': paymentSettings.toMap(),
      'notificationSettings': notificationSettings.toMap(),
      'appSettings': appSettings.toMap(),
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory RestaurantSettings.fromMap(Map<String, dynamic> map) {
    return RestaurantSettings(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'] ?? '',
      address: map['address'] ?? '',
      city: map['city'] ?? '',
      country: map['country'] ?? '',
      timezone: map['timezone'] ?? 'UTC',
      currency: map['currency'] ?? 'TZS',
      logoUrl: map['logoUrl'] ?? '',
      operatingHours: Map<String, OperatingHours>.from(
        (map['operatingHours'] as Map<String, dynamic>? ?? {}).map(
          (key, value) => MapEntry(key, OperatingHours.fromMap(value)),
        ),
      ),
      paymentSettings: PaymentSettings.fromMap(map['paymentSettings'] ?? {}),
      notificationSettings: NotificationSettings.fromMap(map['notificationSettings'] ?? {}),
      appSettings: AppSettings.fromMap(map['appSettings'] ?? {}),
      createdAt: map['createdAt'] != null ? DateTime.parse(map['createdAt']) : null,
      updatedAt: map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
    );
  }

  RestaurantSettings copyWith({
    String? id,
    String? name,
    String? description,
    String? email,
    String? phone,
    String? address,
    String? city,
    String? country,
    String? timezone,
    String? currency,
    String? logoUrl,
    Map<String, OperatingHours>? operatingHours,
    PaymentSettings? paymentSettings,
    NotificationSettings? notificationSettings,
    AppSettings? appSettings,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RestaurantSettings(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      timezone: timezone ?? this.timezone,
      currency: currency ?? this.currency,
      logoUrl: logoUrl ?? this.logoUrl,
      operatingHours: operatingHours ?? this.operatingHours,
      paymentSettings: paymentSettings ?? this.paymentSettings,
      notificationSettings: notificationSettings ?? this.notificationSettings,
      appSettings: appSettings ?? this.appSettings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class OperatingHours {
  final bool isOpen;
  final String openTime;
  final String closeTime;
  final List<String> breaks; // Break periods like "12:00-13:00"

  OperatingHours({
    this.isOpen = true,
    this.openTime = '09:00',
    this.closeTime = '22:00',
    this.breaks = const [],
  });

  Map<String, dynamic> toMap() {
    return {
      'isOpen': isOpen,
      'openTime': openTime,
      'closeTime': closeTime,
      'breaks': breaks,
    };
  }

  factory OperatingHours.fromMap(Map<String, dynamic> map) {
    return OperatingHours(
      isOpen: map['isOpen'] ?? true,
      openTime: map['openTime'] ?? '09:00',
      closeTime: map['closeTime'] ?? '22:00',
      breaks: List<String>.from(map['breaks'] ?? []),
    );
  }
}

class PaymentSettings {
  final bool acceptCash;
  final bool acceptCard;
  final bool acceptMobileMoney;
  final double taxRate;
  final double serviceCharge;
  final String defaultPaymentMethod;
  final Map<String, dynamic> gatewaySettings;

  PaymentSettings({
    this.acceptCash = true,
    this.acceptCard = true,
    this.acceptMobileMoney = true,
    this.taxRate = 0.18, // 18% VAT
    this.serviceCharge = 0.0,
    this.defaultPaymentMethod = 'cash',
    this.gatewaySettings = const {},
  });

  Map<String, dynamic> toMap() {
    return {
      'acceptCash': acceptCash,
      'acceptCard': acceptCard,
      'acceptMobileMoney': acceptMobileMoney,
      'taxRate': taxRate,
      'serviceCharge': serviceCharge,
      'defaultPaymentMethod': defaultPaymentMethod,
      'gatewaySettings': gatewaySettings,
    };
  }

  factory PaymentSettings.fromMap(Map<String, dynamic> map) {
    return PaymentSettings(
      acceptCash: map['acceptCash'] ?? true,
      acceptCard: map['acceptCard'] ?? true,
      acceptMobileMoney: map['acceptMobileMoney'] ?? true,
      taxRate: (map['taxRate'] ?? 0.18).toDouble(),
      serviceCharge: (map['serviceCharge'] ?? 0.0).toDouble(),
      defaultPaymentMethod: map['defaultPaymentMethod'] ?? 'cash',
      gatewaySettings: Map<String, dynamic>.from(map['gatewaySettings'] ?? {}),
    );
  }
}

class NotificationSettings {
  final bool emailNotifications;
  final bool smsNotifications;
  final bool pushNotifications;
  final bool orderNotifications;
  final bool paymentNotifications;
  final bool staffNotifications;
  final String notificationEmail;
  final String notificationPhone;

  NotificationSettings({
    this.emailNotifications = true,
    this.smsNotifications = false,
    this.pushNotifications = true,
    this.orderNotifications = true,
    this.paymentNotifications = true,
    this.staffNotifications = true,
    this.notificationEmail = '',
    this.notificationPhone = '',
  });

  Map<String, dynamic> toMap() {
    return {
      'emailNotifications': emailNotifications,
      'smsNotifications': smsNotifications,
      'pushNotifications': pushNotifications,
      'orderNotifications': orderNotifications,
      'paymentNotifications': paymentNotifications,
      'staffNotifications': staffNotifications,
      'notificationEmail': notificationEmail,
      'notificationPhone': notificationPhone,
    };
  }

  factory NotificationSettings.fromMap(Map<String, dynamic> map) {
    return NotificationSettings(
      emailNotifications: map['emailNotifications'] ?? true,
      smsNotifications: map['smsNotifications'] ?? false,
      pushNotifications: map['pushNotifications'] ?? true,
      orderNotifications: map['orderNotifications'] ?? true,
      paymentNotifications: map['paymentNotifications'] ?? true,
      staffNotifications: map['staffNotifications'] ?? true,
      notificationEmail: map['notificationEmail'] ?? '',
      notificationPhone: map['notificationPhone'] ?? '',
    );
  }
}

class AppSettings {
  final String theme; // 'light', 'dark', 'system'
  final String language;
  final bool enableOfflineMode;
  final bool enableAnalytics;
  final bool enableCrashReporting;
  final int orderAutoRefreshInterval; // seconds
  final int maxOrdersPerPage;
  final bool enableSoundNotifications;
  final double soundVolume;

  AppSettings({
    this.theme = 'system',
    this.language = 'en',
    this.enableOfflineMode = true,
    this.enableAnalytics = true,
    this.enableCrashReporting = true,
    this.orderAutoRefreshInterval = 30,
    this.maxOrdersPerPage = 20,
    this.enableSoundNotifications = true,
    this.soundVolume = 0.8,
  });

  Map<String, dynamic> toMap() {
    return {
      'theme': theme,
      'language': language,
      'enableOfflineMode': enableOfflineMode,
      'enableAnalytics': enableAnalytics,
      'enableCrashReporting': enableCrashReporting,
      'orderAutoRefreshInterval': orderAutoRefreshInterval,
      'maxOrdersPerPage': maxOrdersPerPage,
      'enableSoundNotifications': enableSoundNotifications,
      'soundVolume': soundVolume,
    };
  }

  factory AppSettings.fromMap(Map<String, dynamic> map) {
    return AppSettings(
      theme: map['theme'] ?? 'system',
      language: map['language'] ?? 'en',
      enableOfflineMode: map['enableOfflineMode'] ?? true,
      enableAnalytics: map['enableAnalytics'] ?? true,
      enableCrashReporting: map['enableCrashReporting'] ?? true,
      orderAutoRefreshInterval: map['orderAutoRefreshInterval'] ?? 30,
      maxOrdersPerPage: map['maxOrdersPerPage'] ?? 20,
      enableSoundNotifications: map['enableSoundNotifications'] ?? true,
      soundVolume: (map['soundVolume'] ?? 0.8).toDouble(),
    );
  }
}
