// Order status enum
enum OrderStatus {
  pending,
  confirmed,
  preparing,
  ready,
  outForDelivery,
  delivered,
  completed,
  cancelled,
}

// Order type enum
enum OrderType { delivery, pickup, dineIn }

class OrderModel {
  final String id;
  final String userId;
  final List<Map<String, dynamic>>
  items; // {menuItemId, name, price, quantity, addOns, totalPrice}
  final double subtotal;
  final double deliveryFee;
  final double tax;
  final double total;
  final OrderStatus status;
  final OrderType orderType;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? estimatedDeliveryTime;
  final String? paymentIntentId;
  final String? paymentMethod;
  final Map<String, dynamic>? customerInfo; // {name, phone, address, notes}
  final List<Map<String, dynamic>>? statusHistory; // {status, timestamp, note}
  final String? assignedStaff; // Staff member assigned to this order

  OrderModel({
    required this.id,
    required this.userId,
    required this.items,
    required this.subtotal,
    required this.deliveryFee,
    required this.tax,
    required this.total,
    required this.status,
    required this.orderType,
    required this.createdAt,
    this.updatedAt,
    this.estimatedDeliveryTime,
    this.paymentIntentId,
    this.paymentMethod,
    this.customerInfo,
    this.statusHistory,
    this.assignedStaff,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'items': items,
      'subtotal': subtotal,
      'deliveryFee': deliveryFee,
      'tax': tax,
      'total': total,
      'status': status.name,
      'orderType': orderType.name,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'estimatedDeliveryTime': estimatedDeliveryTime?.toIso8601String(),
      'paymentIntentId': paymentIntentId,
      'paymentMethod': paymentMethod,
      'customerInfo': customerInfo,
      'statusHistory': statusHistory,
      'assignedStaff': assignedStaff,
    };
  }

  factory OrderModel.fromMap(Map<String, dynamic> map) {
    return OrderModel(
      id: map['id'],
      userId: map['userId'],
      items: List<Map<String, dynamic>>.from(map['items'] ?? []),
      subtotal: (map['subtotal'] ?? map['total'] ?? 0.0).toDouble(),
      deliveryFee: (map['deliveryFee'] ?? 0.0).toDouble(),
      tax: (map['tax'] ?? 0.0).toDouble(),
      total: map['total'].toDouble(),
      status: _parseOrderStatus(map['status']),
      orderType: _parseOrderType(map['orderType']),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt:
          map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
      estimatedDeliveryTime:
          map['estimatedDeliveryTime'] != null
              ? DateTime.parse(map['estimatedDeliveryTime'])
              : null,
      paymentIntentId: map['paymentIntentId'],
      paymentMethod: map['paymentMethod'],
      customerInfo:
          map['customerInfo'] != null
              ? Map<String, dynamic>.from(map['customerInfo'])
              : null,
      statusHistory:
          map['statusHistory'] != null
              ? List<Map<String, dynamic>>.from(map['statusHistory'])
              : null,
      assignedStaff: map['assignedStaff'],
    );
  }

  static OrderStatus _parseOrderStatus(dynamic status) {
    if (status is String) {
      switch (status.toLowerCase()) {
        case 'pending':
          return OrderStatus.pending;
        case 'confirmed':
          return OrderStatus.confirmed;
        case 'preparing':
          return OrderStatus.preparing;
        case 'ready':
          return OrderStatus.ready;
        case 'outfordelivery':
          return OrderStatus.outForDelivery;
        case 'delivered':
          return OrderStatus.delivered;
        case 'completed':
          return OrderStatus.completed;
        case 'cancelled':
          return OrderStatus.cancelled;
        default:
          return OrderStatus.pending;
      }
    }
    return OrderStatus.pending;
  }

  static OrderType _parseOrderType(dynamic type) {
    if (type is String) {
      switch (type.toLowerCase()) {
        case 'delivery':
          return OrderType.delivery;
        case 'pickup':
          return OrderType.pickup;
        case 'dinein':
          return OrderType.dineIn;
        default:
          return OrderType.delivery;
      }
    }
    return OrderType.delivery;
  }

  OrderModel copyWith({
    String? id,
    String? userId,
    List<Map<String, dynamic>>? items,
    double? subtotal,
    double? deliveryFee,
    double? tax,
    double? total,
    OrderStatus? status,
    OrderType? orderType,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? estimatedDeliveryTime,
    String? paymentIntentId,
    String? paymentMethod,
    Map<String, dynamic>? customerInfo,
    List<Map<String, dynamic>>? statusHistory,
    String? assignedStaff,
  }) {
    return OrderModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      tax: tax ?? this.tax,
      total: total ?? this.total,
      status: status ?? this.status,
      orderType: orderType ?? this.orderType,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      estimatedDeliveryTime:
          estimatedDeliveryTime ?? this.estimatedDeliveryTime,
      paymentIntentId: paymentIntentId ?? this.paymentIntentId,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      customerInfo: customerInfo ?? this.customerInfo,
      statusHistory: statusHistory ?? this.statusHistory,
      assignedStaff: assignedStaff ?? this.assignedStaff,
    );
  }

  // Helper methods for status display
  String get statusDisplayName {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.preparing:
        return 'Preparing';
      case OrderStatus.ready:
        return 'Ready';
      case OrderStatus.outForDelivery:
        return 'Out for Delivery';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.completed:
        return 'Completed';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get orderTypeDisplayName {
    switch (orderType) {
      case OrderType.delivery:
        return 'Delivery';
      case OrderType.pickup:
        return 'Pickup';
      case OrderType.dineIn:
        return 'Dine In';
    }
  }

  bool get canBeCancelled {
    return status == OrderStatus.pending || status == OrderStatus.confirmed;
  }

  bool get isActive {
    return status != OrderStatus.completed &&
        status != OrderStatus.cancelled &&
        status != OrderStatus.delivered;
  }
}
